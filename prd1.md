# [聘准准] App 核心功能 PRD V1.1

## 1. 文档修订历史

| 版本号 | 修订日期 | 修订人 | 修订内容 |
| :--- | :--- | :--- | :--- |
| V1.1 | 2023-10-27 | 蔡晓旭 | 在V1.0基础上，对所有子页面入口和子页面功能进行详细的功能拆解和描述，增加文档深度。 |
| V1.0 | 2023-10-27 | 蔡晓旭 | 初始版本创建，根据设计稿梳理核心功能需求，并按照底部导航顺序组织文档结构。 |

## 2. 项目概述

本项目是一款面向蓝领、零工、兼职人群的求职与工作管理平台，旨在提供一个高效、可靠、便捷的岗位匹配、上岗打卡、薪资结算的移动端解决方案。App核心功能包括岗位浏览与报名、工作状态管理与打卡、个人中心与财务管理，以及个人信用评级体系。

## 3. 全局功能说明

### 3.1. 底部导航栏 (Bottom Tab Bar)

*   **组成**：由五个独立的 Tab 组成，分别是【求职】、【消息】、【工作】、【班组】、【我的】。
*   **默认页面**：用户首次打开或重新登录 App 后，默认选中的是【工作】Tab。
*   **交互逻辑**：
    *   点击任意 Tab，切换到对应的主页面。
    *   当前选中的 Tab 图标和文字高亮显示（如设计稿中的蓝色）。未选中的为灰色。
    *   【消息】Tab 上的红色角标（如设计稿中的 "6"）表示未读消息数量，该数字由后端接口提供。

### 3.2. 导航栏 (Navigation Bar)

*   **标准样式**：页面顶部为导航栏，通常包含返回按钮（`<`）、页面标题和右侧的操作按钮（如有）。
*   **返回逻辑**：在所有非一级页面（即非底部 Tab Bar 对应的页面）的导航栏左侧，都有一个返回按钮。点击该按钮，返回到上一个页面。

---

## 4. 模块详细功能需求

### **模块一：求职 (Job Search)**

此模块是App的岗位发现与搜索中心，为用户提供丰富的岗位信息。

#### **4.1. 页面整体结构**
*   **页面名称**: 求职
*   **页面路径**: 通过点击底部导航栏【求职】Tab进入。

#### **4.2. 顶部搜索筛选区**

*   **入口1：【城市选择】**
    *   **入口描述**: 位于导航栏左上角，显示当前城市，如“北京▼”。
    *   **交互逻辑**: 点击后，从屏幕下方或以新页面的形式弹出【城市选择】列表。
    *   **子页面：【城市选择】页面/弹窗**
        *   **页面目的**: 方便用户切换求职地点，查看不同城市的岗位。
        *   **页面元素**:
            1.  **当前定位城市**: 顶部显示通过GPS定位获取的当前城市，如“定位城市：北京市”，右侧有“重新定位”按钮。
            2.  **热门城市列表**: 展示几个主要的招聘城市，如北京、上海、广州、深圳等。
            3.  **城市索引列表**: 按照字母A-Z排序的全国城市列表，右侧有A-Z的快速索引导航条。
        *   **交互逻辑**:
            *   点击任意城市，选择器关闭，【求职】页的城市切换为所选城市，并自动刷新岗位列表以显示该城市的岗位。
            *   点击“重新定位”将再次请求定位权限并更新定位城市。

*   **入口2：【搜索框】**
    *   **入口描述**: 显示灰色提示文字“请输入你想找的工作”。
    *   **交互逻辑**: 点击后，跳转至独立的【搜索】页面。
    *   **子页面：【搜索】页面**
        *   **页面目的**: 提供更精确的岗位搜索功能。
        *   **页面元素**:
            1.  **搜索输入框**: 页面顶部，自动获取焦点，弹出键盘。
            2.  **取消/搜索按钮**: 输入框右侧为“搜索”按钮，或在无输入时为“取消”按钮，点击“取消”返回【求职】页。
            3.  **历史搜索记录**: 以标签形式展示用户最近的5-10条搜索关键词。点击标签可直接进行搜索。有“清空历史记录”按钮。
            4.  **热门搜索**: 以标签形式展示平台内当前搜索热度高的关键词。
        *   **交互逻辑**:
            *   输入关键词后点击“搜索”按钮，跳转至【搜索结果】页面，展示与关键词匹配的岗位列表。
            *   【搜索结果】页面布局与【求职】页的岗位列表类似，但顶部会显示“‘关键词’的搜索结果”。

*   **入口3：【筛选▼】**
    *   **入口描述**: 位于排序/精选Tabs的最右侧。
    *   **交互逻辑**: 点击后，从屏幕右侧或下方滑出【筛选】面板。
    *   **子页面：【筛选】面板**
        *   **页面目的**: 提供多维度筛选条件，帮助用户精准定位目标岗位。
        *   **页面元素**:
            1.  **薪资要求**: 提供预设的薪资范围（如“3-5k”、“5-8k”等）和自定义输入范围。
            2.  **结算方式**: 多选项，如“日结”、“周结”、“月结”。
            3.  **福利待遇**: 多选项，如“包吃”、“包住”、“五险一金”、“交通补助”等。
            4.  **工作性质**: 多选项，如“全职”、“兼职”、“实习”。
            5.  **重置按钮**: 清除所有已选筛选条件。
            6.  **确定按钮**: 应用所选筛选条件，关闭面板并刷新【求职】页的岗位列表。

#### **4.3. 岗位列表**

*   **入口4：【岗位卡片】**
    *   **入口描述**: 列表中的任意一个岗位信息卡片。
    *   **交互逻辑**: 点击卡片任意位置（除“报名”按钮外），跳转至【岗位详情】页面。
    *   **子页面：【岗位详情】页面**
        *   **页面目的**: 全面展示单个岗位的详细信息，帮助用户决策。
        *   **页面元素**:
            1.  **岗位核心信息**: 职位名称、薪资范围、工作地点、招聘人数、岗位标签。
            2.  **职位描述**: 详细的工作内容、职责要求。
            3.  **任职要求**: 对年龄、经验、学历等方面的具体要求。
            4.  **工作时间/福利待遇**: 详细的上下班时间、休息制度、福利项目。
            5.  **工作地点**: 显示详细地址，并内嵌一个小地图展示位置，提供“导航”按钮。
            6.  **企业信息**: 公司名称、简介、规模等。点击可进入【企业主页】。
            7.  **底部操作栏**:
                *   **【收藏】按钮**: 点击后收藏该岗位，按钮变为“已收藏”。
                *   **【分享】按钮**: 点击调起系统分享，分享此岗位链接。
                *   **【立即报名】按钮**: 功能同列表页的【报名】按钮。

*   **岗位列表内容说明**
    *   **功能描述**: 以卡片形式无限滚动加载岗位信息。
    *   **岗位卡片元素 (以“搬运工”为例)**:
        *   **职位名称**: “搬运工”，带“短”标签。
        *   **薪资**: “8 元/件” 或 “5800-8800 元/月”。
        *   **标签**: “计时计件”、“岗位班制”、“月休类型”、“包住”、“餐补”等。
        *   **要求**: “男女不限 | 20-30 | 日结”。
        *   **地点**: “劲松街道”。
        *   **简介/亮点**: “该岗位为快速岗结算快麻烦溜点的来几凡”。
        *   **【报名】按钮**:
            *   **交互**: 点击后，若用户满足岗位要求（如已实名），则直接提示“报名成功”，或进入一个待企业确认的流程。若不满足，则提示原因（如“请先完成实名认证”）。报名成功后，该岗位应在【工作】页的“待开始”中显示。

---

### **模块二：消息 (Messages)**

*   **需求说明**: 当前提供的设计稿中未包含【消息】页面。根据底部导航栏的角标（如'6'）推断，此模块用于展示系统通知、与班组长或企业的聊天消息、岗位动态提醒等。
*   **待设计功能点 (V2.0)**:
    *   **消息列表**: 展示所有会话或通知的列表，包含头像、发送方、消息摘要、时间和未读红点。
    *   **会话类型**: 需区分系统通知、互动消息（如报名成功、审核通过）、私聊消息。
    *   **聊天详情页**: 支持发送文本、图片、表情等，展示聊天记录。
    *   **消息设置**: 提供消息免打扰、接收设置等功能。
*   **当前版本开发**: 仅需实现Tab入口，点击后可提示“功能开发中，敬请期待”或展示一个简单的静态页。未读消息角标功能需预留接口。

---

### **模块三：工作 (Work)**

此模块是用户的个人工作台，展示当前和未来的工作安排及核心操作。

#### **5.1. 页面整体结构**
*   **页面名称**: 工作首页
*   **页面路径**: App启动默认页，或通过点击底部导航栏【工作】Tab进入。
*   **顶部信息区**: 显示页面标题“工作首页”和动态获取的当前日期。
*   **工作状态概览**:
    *   **功能描述**: 以卡片形式快速展示【进行中】、【待开始】、【未结算】、【已结算】四种状态的工作数量统计。
    *   **交互逻辑**: 点击任一卡片，跳转至【工作记录】页面，并自动筛选到对应状态的工作列表。

#### **5.2. 当前工作任务卡片**
*   **功能描述**: 详细展示用户当前最紧急的一项工作任务。若无任务，则展示空状态。
*   **页面元素**: 任务标题（计件快递）、状态（正常）、时间、奖励、公司、地址、距离等。
*   **【查看路线】按钮**: 点击调用本地地图App进行导航。
*   **快捷链接**: 【上岗小贴士】、【放弃岗位须知】。点击后显示相关信息。
*   **操作按钮**:
    *   **【联系班组长】**: 触发联系操作（电话/IM）。
    *   **【放弃】**: 点击后二次确认，执行放弃逻辑。

#### **5.3. 打卡模块**
*   **功能描述**: 提供上班和下班的打卡功能。
*   **页面元素与交互**:
    *   **状态提示**: “工作即将开始”，动态变化。
    *   **【上班打卡】/【下班打卡】按钮**:
        *   根据时间、状态和地理位置（需定位校验）判断是否可点击。
        *   点击后执行打卡逻辑，成功或失败均有明确提示，并更新按钮和任务状态。

#### **5.4. 抢单大厅入口**
*   **功能描述**: 引导用户进入抢单模式或岗位列表。
*   **页面元素与交互**:
    *   点击Banner区域，跳转至【求职】页面。
    *   点击【添加接单启动提醒】，创建日历或App内提醒。
    *   点击【设置接单过滤】，跳转至【求职偏好】设置页面。

#### **5.5. 快捷功能入口与子页面**

*   **入口1：【求职偏好】**
    *   **交互逻辑**: 点击后，跳转至【求职偏好】设置页面。
    *   **子页面：【求职偏好】页面**
        *   **页面目的**: 用户设定期望的工作类型，以便平台进行更精准的岗位推荐。
        *   **页面元素**: 期望职位、期望薪资、期望工作地点、工作类型等设置项及保存按钮。

*   **入口2：【我的收藏】**
    *   **交互逻辑**: 点击后，跳转至【我的收藏】页面。
    *   **子页面：【我的收藏】页面**
        *   **页面目的**: 集中展示用户收藏的岗位，方便后续查看和报名。
        *   **页面元素**: 收藏的岗位列表，样式同【求职】页卡片。支持取消收藏。
        *   **空状态**: 若无收藏，则显示引导文案和“去求职”按钮。

*   **入口3：【打卡记录】**
    *   **交互逻辑**: 点击后，跳转至【打卡记录】页面。
    *   **子页面：【打卡记录】页面**
        *   **页面目的**: 展示用户所有的历史出勤记录。
        *   **页面元素**: 月份筛选器和按天分组的打卡记录列表，每条记录包含工作信息、打卡时间和状态标签（正常、迟到等）。

*   **入口4：【工作记录】**
    *   **交互逻辑**: 点击后，跳转至【工作记录】页面。
    *   **子页面：【工作记录】页面**
        *   **页面目的**: 聚合管理用户所有报名过的工作。
        *   **页面元素**: 状态筛选Tabs（全部、进行中等）和对应的工作列表。点击列表项可进入该次工作的详情页。

---

### **模块四：班组 (Team/Group)**

*   **需求说明**: 当前提供的设计稿中未包含【班组】页面。此模块可能用于用户与工友、班组长进行协作和沟通。
*   **待设计功能点 (V2.0)**:
    *   **我的班组列表**: 展示用户已加入的所有班组。
    *   **班组详情**: 显示班组成员、班组公告、共享文件（如排班表）等。
    *   **班组沟通**: 内嵌的即时通讯功能或指向【消息】模块中的群聊。
*   **当前版本开发**: 仅需实现Tab入口，点击后可提示“功能开发中，敬请期待”或展示一个简单的静态页。

---

### **模块五：我的 (Me)**

此模块是用户的个人中心，聚合了个人信息、财务、设置等功能。

#### **6.1. 页面主体结构**
*   **个人信息头图**: 显示头像、昵称、等级、实名状态、编辑简历入口、信用分入口。
*   **数据统计**: 展示【工作企业】、【累计工作】、【累计收入】。
*   **本地求职群入口**: 展示微信群二维码。
*   **账户与银行卡模块**: 显示余额，提供绑定银行卡、提现、预支、查看明细等入口。
*   **常用功能区**: 图标矩阵，提供求职偏好、收藏、记录、认证、设置等各类功能的快捷入口。
*   **分享赚钱Banner**: 引导用户分享App。

#### **6.2. 核心功能与子页面详解**

*   **入口1：【编辑简历】/【我的简历】**
    *   **交互逻辑**: 点击后跳转至【我的简历】页面。
    *   **子页面：【我的简历】页面**
        *   **页面目的**: 用户创建和维护自己的在线简历。
        *   **页面元素**: 分为基本信息、求职意向、工作经历、教育经历、自我评价等模块，提供新增、编辑、删除和保存功能。

*   **入口2：【绑定银行卡】**
    *   **交互逻辑**: 点击“去绑定”进入【银行卡绑定】流程。
    *   **子页面：【银行卡绑定】流程**
        *   **页面目的**: 引导用户安全地绑定用于接收薪资的银行卡。
        *   **流程步骤**: 1.身份验证 -> 2.填写卡号 -> 3.填写预留手机号 -> 4.短信验证 -> 5.完成。

*   **入口3：【提现】**
    *   **交互逻辑**: 点击后跳转至【提现】页面。
    *   **子页面：【提现】页面**
        *   **页面目的**: 用户将账户余额提取到已绑定的银行卡。
        *   **页面元素**: 显示提现银行卡、可提现金额、金额输入框、到账说明和确认按钮。需要支付密码验证。

*   **入口4：【查看明细】**
    *   **交互逻辑**: 点击后跳转至【收支明细】页面。
    *   **子页面：【收支明细】页面**
        *   **页面目的**: 透明地展示用户账户的所有资金流水。
        *   **页面元素**: 提供按类型（收入/支出）和月份的筛选器，以及详细的流水列表。

*   **入口5：【实名认证】**
    *   **交互逻辑**: 点击“未实名”或“实名认证”图标跳转。
    *   **子页面：【实名认证】页面**
        *   **页面目的**: 验证用户身份的真实性。
        *   **页面元素**: 身份证正反面上传、OCR自动识别信息、人脸识别（可选）、提交审核。页面需处理未认证、审核中、成功、失败四种状态。

*   **入口6：【设置中心】**
    *   **交互逻辑**: 点击“设置中心”图标跳转。
    *   **子页面：【设置中心】页面**
        *   **页面目的**: 提供App级别的通用设置。
        *   **页面元素**: 包含账号与安全、消息通知、清除缓存、关于我们、隐私政策、退出登录等列表项。

*   **入口7：【我的评分】**
    *   **入口描述**: 点击个人信息区的信用分(如 'A+')或功能矩阵中的【诚信计分】入口。
    *   **交互逻辑**: 跳转至【我的评分】页面。
    *   **子页面：【我的评分】页面**
        *   **页面目的**: 向用户展示其信用评分、构成及历史记录，建立平台的信用体系。
        *   **导航栏**: 标题“我的评分”，右侧“规则介绍”链接，点击可查看评分规则说明。
        *   **评分仪表盘**:
            *   **功能**: 可视化展示用户的综合评分。
            *   **元素**: 从D到A等级的彩色刻度条，指针指向用户当前分数（如70），并显示最终评级（如 A+）。
        *   **行为统计**:
            *   **功能**: 统计影响评分的关键负面行为次数。
            *   **元素**: 【爽约】: 1次, 【放弃】: 1次, 【迟到】: 1次。数据动态获取。
        *   **评分历史记录**:
            *   **功能**: 以列表形式展示所有与评分相关的行为记录。
            *   **元素**:
                *   **筛选器**: 支持按【全部】或按【月份】进行筛选。
                *   **记录列表**: 每条记录包含日期、企业、地址和行为标签（如【爽约】、【放弃】、【迟到】、【早退】）。不同行为用不同颜色标识。
                *   **操作**: 每条记录右侧的【▶】按钮，点击可跳转至该次工作的详情页或相关凭证页。

---

### **7. 非功能性需求**
*   **性能**: 页面加载时间应在3秒以内，列表滚动流畅不卡顿。
*   **兼容性**: 需兼容主流的 iOS 和 Android 系统版本（如 iOS 13+，Android 6.0+）。
*   **数据安全**: 用户个人信息、银行卡信息、密码等敏感数据需加密传输和存储。
*   **空状态/错误状态**: 所有列表和页面在无数据、加载失败、网络错误时，都应有明确的UI提示和操作引导（如“暂无数据”、“加载失败，请重试”）。