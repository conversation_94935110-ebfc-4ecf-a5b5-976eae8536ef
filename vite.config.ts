import { defineConfig, loadEnv } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'

// https://vite.dev/config/
export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd())
  console.log(`当前环境：${mode}`)

  return {
    base: './',
    plugins: [react()],
    resolve: {
      alias: [
        { find: '@', replacement: path.normalize(path.resolve(process.cwd(), 'src')) },
        { find: '@projects', replacement: path.normalize(path.resolve(process.cwd(), 'projects')) }
      ]
    },
    server: {
      headers: {
        'Cross-Origin-Embedder-Policy': 'require-corp',
        'Cross-Origin-Opener-Policy': 'same-origin'
      },
      proxy: {
        '/api': {
          target: env.VITE_API_BASE,
          changeOrigin: true,
          ws: true,
          toProxy: true,
          rewrite: (path: string) => path.replace(new RegExp(`^/api`), '')
        },
        '/upload': {
          target: env.VITE_AI_API_BASE,
          changeOrigin: true,
          ws: true,
          toProxy: true,
          rewrite: (path: string) => path.replace(new RegExp(`^/upload`), '')
        },
        '/oa': {
          target: env.VITE_OA_BASE_URL,
          changeOrigin: true,
          ws: true,
          toProxy: true,
          rewrite: (path: string) => path.replace(new RegExp(`^/oa`), '')
        }
      }
    }
  }
})
