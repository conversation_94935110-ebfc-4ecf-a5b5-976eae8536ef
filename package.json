{"name": "copilot-toolbox", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite --host", "stage": "vite --host --mode production", "test": "vite --host --mode test", "build": "vite build", "build:test": "vite build --mode test", "build:prod": "vite build --mode production", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@ant-design/icons": "^5.5.2", "@vant/area-data": "^2.0.0", "@webcontainer/api": "1.5.1-internal.5", "@xterm/xterm": "^5.5.0", "@xyflow/react": "^12.5.6", "ahooks": "^3.8.4", "antd": "^5.23.0", "axios": "^1.7.9", "dagre": "^0.8.5", "jspdf": "^3.0.1", "less": "^4.2.1", "pdfjs-dist": "2.2.228", "react": "^18.3.1", "react-dom": "^18.3.1", "react-helmet": "^6.1.0", "react-markdown": "^9.0.3", "react-router-dom": "^6.28.1", "react-syntax-highlighter": "^15.6.1", "remark-breaks": "^4.0.0", "remark-gfm": "^4.0.0", "remark-math": "^6.0.0"}, "devDependencies": {"@eslint/js": "^9.17.0", "@types/dagre": "^0.7.52", "@types/file-saver": "^2.0.7", "@types/markdown-it": "^14.1.2", "@types/node": "^22.10.5", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@types/react-helmet": "^6.1.11", "@types/react-json-editor-ajrm": "^2.5.6", "@types/react-syntax-highlighter": "^15.5.13", "@types/react-window": "^1.8.8", "@vitejs/plugin-react": "^4.3.4", "docx": "^9.3.0", "docx-preview": "^0.3.5", "echarts": "^5.6.0", "echarts-for-react": "3.0.2", "eslint": "^9.17.0", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-react-refresh": "^0.4.16", "file-saver": "^2.0.5", "globals": "^15.14.0", "jotai": "^2.12.3", "jsoneditor": "^10.2.0", "markdown-it": "^14.1.0", "react-json-editor-ajrm": "^2.5.14", "react-window": "1.8.10", "rehype-raw": "^7.0.0", "typescript": "~5.6.3", "typescript-eslint": "^8.19.1", "use-resize-observer": "^9.1.0", "vite": "^6.0.7", "xlsx": "^0.18.5"}}