import {createContext, ReactNode, useContext, useEffect, useState} from "react";
import {message} from "antd";
import {difyLogin, difyProfile} from "../api/login";
import {ssoApi} from "../api/sso";
import {oaLogin} from "../api/oa";

const SSO_CLIENT_ID = import.meta.env.VITE_SSO_CLIENT_ID || 'YOUR_CLIENT_ID';
const SSO_ENABLED = import.meta.env.VITE_SSO_ENABLED === 'true';
const SSO_LOGIN_URL = import.meta.env.VITE_SSO_LOGIN_URL || '';

// 定义用户认证数据类型
interface AuthData {
  access_token: string;
  refresh_token: string;

  [key: string]: any;
}

// 定义用户数据类型
interface User {
  email: string;
  name: string;

  [key: string]: any;
}

interface AuthContextType {
  user: User | null;
  authData: AuthData | null;
  loading: boolean;
  login: (email: string, password: string, loginType: string, access_token: string, refresh_token: string) => Promise<any>;
  logout: () => void;
  checkAuth: () => Promise<boolean>;
  isAuthenticated: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export function AuthProvider({children}: AuthProviderProps) {
  const [user, setUser] = useState<User | null>(null);
  const [authData, setAuthData] = useState<AuthData | null>(null);
  const [loading, setLoading] = useState(true);

  // 获取用户资料
  const fetchUserProfile = async (auth: AuthData) => {
    try {
      const response = await difyProfile();

      if (response.status === 401) {
        // 登录过期，清除用户数据
        setAuthData(null);
        setUser(null);
        localStorage.removeItem("user");
        message.error("登录已过期，请重新登录");
        return false;
      }

      if (!response.ok) {
        throw new Error("获取用户信息失败");
      }

      const userData = await response.json();
      setUser(userData.data || userData);
      return true;
    } catch (error) {
      console.error("获取用户资料失败:", error);
      return false;
    }
  };

  // 检查用户认证状态
  const checkAuth = async (): Promise<boolean> => {
    const savedAuth = localStorage.getItem("user");
    const loginType = localStorage.getItem("loginType");
    
    if (!savedAuth || !loginType) {
      setLoading(false);
      return false;
    }

    try {
      const parsedAuth = JSON.parse(savedAuth);
      // 检查是否有登录成功的数据结构
      if (parsedAuth.result === "success" && parsedAuth.data && parsedAuth.data.access_token) {
        setAuthData(parsedAuth.data);
        // 获取用户资料
        if (loginType === 'dify') {
          const isValid = await fetchUserProfile(parsedAuth.data);
          setLoading(false);
          return isValid;
        } else if (loginType === 'sso') {
          const isValid = await fetchSsoUserProfile(parsedAuth.data.access_token);
          setLoading(false);
          return isValid;
        } else if (loginType === 'oa') {
          // OA登录不需要额外验证，直接返回true
          setLoading(false);
          return true;
        }
      } else {
        setLoading(false);
        return false;
      }
    } catch (e) {
      console.error("解析存储的认证数据失败:", e);
      localStorage.removeItem("user");
      setLoading(false);
      return false;
    }
    
    setLoading(false);
    return false;
  };

  // 初始化时检查用户认证状态
  useEffect(() => {
    checkAuth();
  }, []);

  const login = async (email: string, password: string, loginType: string, access_token: string, refresh_token: string) => {
    if (loginType === 'dify') {
      return difylogin(email, password);
    } else if (loginType === 'sso') {
      return ssologin(access_token, refresh_token);
    } else if (loginType === 'oa') {
      return oalogin(email, password);
    }
    
    // 添加默认错误返回
    return {
      result: "error",
      message: "未知的登录类型"
    };
  }

  // OA登录方法
  const oalogin = async (username: string, password: string) => {
    try {
      const result = await oaLogin(username, password);
      
      const loginData = {
        result: "success",
        data: {
          access_token: result.token,
          refresh_token: "", // OA系统可能不提供refresh_token
          username: result.realName,
          email: result.username
        }
      };

      // 保存认证数据
      setAuthData(loginData.data);
      localStorage.setItem("user", JSON.stringify(loginData));
      localStorage.setItem("loginType", 'oa');

      // 设置用户信息
      const oaUser: User = {
        name: result.username,
        email: result.username, // 使用username作为email
        username: result.username
      };
      setUser(oaUser);

      return loginData;
    } catch (error) {
      if (error instanceof Error) {
        console.log(error);
        throw error;
      }
      throw new Error("OA登录过程中发生错误");
    }
  };

  // 登录方法 - 使用实际API
  const difylogin = async (email: string, password: string) => {
    try {
      const response = await difyLogin(email, password);
      console.log(response)
      if (!response.ok) {
        const errorData = await response.json();

        if (errorData.code) {
          switch (errorData.code) {
            case "account_not_found":
              throw new Error(`登录失败，账户${email}不存在`);
            case "email_or_password_mismatch":
              throw new Error("登录失败，密码或账号错误");
            case "invalid_param":
              throw new Error("登录失败，密码或账号错误");
            default:
              break
          }
        } else {
          throw new Error(`登录失败：${errorData.message}`);
        }
      }

      const loginData = await response.json();

      // 验证返回的数据格式
      if (loginData.result !== "success" || !loginData.data || !loginData.data.access_token) {
        throw new Error("登录返回数据格式不正确");
      }

      // 保存认证数据
      setAuthData(loginData.data);
      localStorage.setItem("user", JSON.stringify(loginData));
      localStorage.setItem("loginType", 'dify');

      // 获取用户资料
      await fetchUserProfile(loginData.data);

      return loginData;
    } catch (error) {
      if (error instanceof Error) {
        console.log(error);
        throw error;
      }
      throw new Error("登录过程中发生错误");
    }
  };
  // sso登录方法 - 使用实际API
  const ssologin = async (access_token: string, refresh_token: string) => {
    try {
      
      const loginData = {
        result: "success",
        data: {
          access_token,
          refresh_token
        }
      };

      // 保存认证数据
      setAuthData(loginData.data);
      localStorage.setItem("user", JSON.stringify(loginData));
      localStorage.setItem("loginType", 'sso');

      // 获取用户资料
      await fetchSsoUserProfile(access_token);

      return loginData;
    } catch (error) {
      if (error instanceof Error) {
        console.log(error);
        throw error;
      }
      throw new Error("登录过程中发生错误");
    }
  };
  // SSO 用户信息获取（假设有 ssoApi.getUserInfoByToken）
  const fetchSsoUserProfile = async (accessToken: string) => {
    try {
      // 这里假设有 ssoApi.getUserOpenId 和 ssoApi.getUserInfo
      const openIdResp = await ssoApi.getUserOpenId({ access_token: accessToken });
      const openId = openIdResp.openid;
      if (!openId) throw new Error('SSO openid 获取失败');
      const userInfoResp = await ssoApi.getUserInfo({ access_token: accessToken, oauth_consumer_key: SSO_CLIENT_ID, openid: openId });
      const userInfo = userInfoResp.userinfo;
      if (!userInfo) throw new Error('SSO 用户信息获取失败');
      // 映射 SSO 用户信息到 User，保证类型安全
      const loginname = userInfo.loginname || '';
      const name = userInfo.username || loginname || '';
      const email = userInfo.loginname || '';
      
      const appUser: User = {
        name,
        email,
        loginname: loginname || undefined,
      };
      setUser(appUser);
      return true;
    } catch (error) {
      console.error("获取SSO用户资料失败:", error);
      return false;
    }
  };

  // 登出方法
  const logout = (): void => {
    setUser(null);
    setAuthData(null);
    const loginType = localStorage.getItem("loginType");
    localStorage.removeItem("user");
    localStorage.removeItem("loginType");
    
    message.success("已成功退出登录");
    
    // 如果SSO登录已启用且有配置SSO登录URL，则跳转到SSO登录页面
    // if (SSO_ENABLED && SSO_LOGIN_URL && loginType === 'sso') {
    //   window.location.href = SSO_LOGIN_URL;
    // }
  };

  const authValues: AuthContextType = {
    user,
    authData,
    loading,
    login,
    logout,
    checkAuth,
    isAuthenticated: !!authData
  };

  return (
    <AuthContext.Provider value={authValues}>
      {children}
    </AuthContext.Provider>
  );
}

// 自定义hook来使用认证上下文
export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
}
