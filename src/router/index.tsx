/** 配置管理路由主文件 */
import {lazy, Suspense} from 'react'
import {createHashRouter, Navigate} from 'react-router-dom'
import {Spin} from 'antd'
import App from '@/App'
import {AuthProvider} from '../contexts/auth-context'
import AuthRoute from '../component/auth-route'

const Loading = () => (
  <div
    style={{
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      height: '100vh',
    }}
  >
    <Spin size="large"/>
  </div>
)

// 使用 React.lazy 懒加载组件
const NotFound = lazy(() => import('@/pages/not-found'))
const EmployeeKnowledgeAnswer = lazy(
  () => import('@/pages/employee-knowledge-answer')
)
const ContractVerification = lazy(
  () => import('@/pages/contract-verification')
)
const Login = lazy(() => import('@/pages/login'))
const SsoCallbackPage = lazy(() => import('@/pages/sso-callback'))
const SsoAuthPage = lazy(() => import('@/pages/sso-auth'))

export const routes = [
  {
    path: '/',
    element: <AuthProvider><App/></AuthProvider>,
    children: [
      {
        path: '/employee-knowledge-answer',
        element: (
          <Suspense fallback={<Loading/>}>
            <AuthRoute element={<EmployeeKnowledgeAnswer/>}/>
          </Suspense>
        ),
        title: '两融业务百问百答',
      },
      {
        path: '/contract-verification',
        element: (
          <Suspense fallback={<Loading/>}>
            <AuthRoute element={<ContractVerification/>}/>
          </Suspense>
        ),
        title: '合同校验助手',
      },
      {
        path: '/not-found',
        element: (
          <Suspense fallback={<Loading/>}>
            <NotFound/>
          </Suspense>
        ),
        errorElement: <></>,
        title: '404 未找到',
      },
    ],
  },
  {
    path: '/login',
    element: (
      <AuthProvider>
        <Suspense fallback={<Loading/>}>
          <Login/>
        </Suspense>
      </AuthProvider>
    ),
    title: '用户登录',
  },
  {
    path: '/auth/sso-callback',
    element: (
      <AuthProvider>
        <Suspense fallback={<Loading />}>
          <SsoCallbackPage />
        </Suspense>
      </AuthProvider>
    ),
    title: 'SSO登录处理',
  },
  {
    path: 'auth/sso',
    element: (
      <Suspense fallback={<Loading />}>
        <SsoAuthPage />
      </Suspense>
    ),
    title: 'SSO认证重定向',
  },
  {
    path: '*',
    element: <Navigate to="/not-found" replace/>,
  },
]

export const routers = createHashRouter(routes)
