import {Outlet, useLocation, useNavigate} from "react-router-dom";
import useDocumentTitle from "@/hooks/use-document-title.ts";
import React, {useEffect, useRef, useState} from "react";
import {useAuth} from "./contexts/auth-context";
import {Button, Flex, Layout, Modal, Space, Spin} from "antd";
import {LogoutOutlined} from "@ant-design/icons";

const MenuItem = ({path, children}) => {
  const navigate = useNavigate();
  return <a target="_blank" onClick={(e) => {
    e.preventDefault();
    navigate(path);
  }}>
    {children}
  </a>
}

const menu = [
  {
    key: "/employee-knowledge-answer",
    name: "两融业务百问百答",
    label: <MenuItem path="/employee-knowledge-answer">两融业务百问百答</MenuItem>,
  },
  {
    key: "/contract-verification",
    name: "合同校验助手",
    label: <MenuItem path="/contract-verification">合同校验助手</MenuItem>,
  },
]

const App = () => {
  useDocumentTitle(import.meta.env["VITE_DOCUMENT_TITLE"]);
  const {user, isAuthenticated, loading, checkAuth, logout} = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const [validating, setValidating] = useState(false);
  const [modal, modalContextHolder] = Modal.useModal()
  // 跟踪路由变化，避免重复检查
  const lastCheckedRoute = useRef("");
  const checkInProgressRef = useRef(false);

  // 检查用户是否已登录，如果未登录则跳转到登录页面
  useEffect(() => {
    // 如果在加载中或已在验证中，不执行操作
    if (loading || validating || checkInProgressRef.current) {
      return;
    }

    // 如果当前路径已经检查过，避免重复检查
    if (lastCheckedRoute.current === location.pathname) {
      return;
    }

    // 如果未认证，直接跳转登录页
    if (!isAuthenticated) {
      navigate(`/login?redirect=${encodeURIComponent(location.pathname)}`);
      return;
    }

    // 需要验证认证状态
    checkInProgressRef.current = true;
    setValidating(true);

    // 验证会话
    const validateSession = async () => {
      try {
        const isValid = await checkAuth();

        if (!isValid) {
          navigate(`/login?redirect=${encodeURIComponent(location.pathname)}`);
        } else {
          // 更新已检查的路径
          lastCheckedRoute.current = location.pathname;
          if (location.pathname === "/") {
            navigate("/employee-knowledge-answer")
          }
        }
      } catch (error) {
        console.error("验证会话失败:", error);
        navigate(`/login?redirect=${encodeURIComponent(location.pathname)}`);
      } finally {
        setValidating(false);
        checkInProgressRef.current = false;
      }
    };

    validateSession();
  }, [user, isAuthenticated, loading, navigate, location.pathname, checkAuth, validating]);

  const handleProfileClick = async () => {
    const confirm = await modal.confirm({
      title: "您确定退出系统吗？"
    })
    if (!confirm) {
      return
    }
    logout()
  }

  if (loading || validating) {
    return (
      <div style={{height: "100vh", display: "flex", justifyContent: "center", alignItems: "center"}}>
        <Spin size="large" tip="加载中..."/>
      </div>
    );
  }
  return <Layout style={{height: '100vh', overflow: 'hidden'}}>
    {modalContextHolder}
    <Flex className="scene-header" align="center" gap={"large"} justify={"space-between"}>
      <h1 className="title">{document.title}</h1>
      <Flex
        justify="space-between"
        align="center"
        gap="middle"
        style={{
          height: "5em"
        }}
      >
        {/* <Space style={{transform: "translateX(-30%)"}}>
          {
            menu.map((item, index) => {
              return <Button className={location.pathname == item.key ? "menu-active" : ""} key={index}
                             onClick={() => navigate(item.key)}>{item.name}</Button>
            })
          }
        </Space> */}
        <h2 className="title">
          {user?.name}
        </h2>
        <Button icon={<LogoutOutlined/>} onClick={handleProfileClick}></Button>
      </Flex>
    </Flex>
    <Outlet/>
  </Layout>;
};

export default App;
