import React, {useEffect, useState} from "react";
import {Navigate, useLocation} from "react-router-dom";
import {message, Spin} from "antd";
import {useAuth} from "../../contexts/auth-context";

interface AuthRouteProps {
  element: React.ReactNode;
}

const AuthRoute: React.FC<AuthRouteProps> = ({element}) => {
  const {isAuthenticated, loading, checkAuth} = useAuth();
  const location = useLocation();
  const [validating, setValidating] = useState(false);
  const [isValid, setIsValid] = useState(false);

  useEffect(() => {
    // 如果已经知道未认证，不需要验证
    if (!isAuthenticated) {
      setIsValid(false);

      return;
    }

    // 已认证但需要验证有效性
    if (isAuthenticated && !validating && !isValid) {
      setValidating(true);

      const validate = async () => {
        try {
          const valid = await checkAuth();
          setIsValid(valid);
        } catch (error) {
          setIsValid(false);
          message.error("验证登录状态失败，请重新登录");
        } finally {
          setValidating(false);
        }
      };

      validate();
    }
  }, [isAuthenticated, checkAuth, validating, isValid]);

  if (loading || (isAuthenticated && validating)) {
    return (
      <div style={{height: "100vh", display: "flex", justifyContent: "center", alignItems: "center"}}>
        <Spin size="large" tip="验证登录状态..."/>
      </div>
    );
  }

  return isAuthenticated && (isValid || !validating) ? (
    <>{element}</>
  ) : (
    <Navigate to={`/login?redirect=${encodeURIComponent(location.pathname)}`} replace/>
  );
};

export default AuthRoute;
