import {memo, useEffect, useRef, useState} from 'react'
import ReactMarkdown from 'react-markdown'
import remarkGfm from 'remark-gfm'
import rehypeRaw from 'rehype-raw'
import './index.less'

const StreamTypewriter = ({
                            text = '',
                            stream = true,
                            speed = 30,
                            end = false,
                            stop = false, // 新增 stop停止输出 属性
                            components = {},
                            onchange = () => {
                            }
                          }) => {
  const [displayText, setDisplayText] = useState('')
  const processingIndex = useRef(0)
  const prevText = useRef('')

  // 处理文本差异
  const getNewContent = () => {
    const newPart = text.slice(prevText.current.length)
    prevText.current = text // 更新已处理的基准文本
    return newPart
  }

  // 打字机核心逻辑
  useEffect(() => {
    if (!stream) {
      setDisplayText(text);
      return
    }
    if (text.length === 0) {
      setDisplayText('')
      return
    }
    let animator: NodeJS.Timeout | undefined = undefined

    const typeNextChar = () => {
      if (processingIndex.current < text.length && !stop) {
        // 如果 stop 为 true，停止流式输出
        setDisplayText(prev => {
          const newChar = text[processingIndex.current]
          processingIndex.current += 1
          onchange()
          return prev + newChar
        })
        animator = setTimeout(typeNextChar, speed)
      }
    }

    if (getNewContent().length > 0 && !stop) {
      // 如果 stop 为 true，不启动新的动画
      animator = setTimeout(typeNextChar, speed)
    }

    return () => {
      if (animator) clearTimeout(animator)
    }
  }, [text, speed, stop])

  // 解析displayText内容
  const thinkContent = displayText.split("</think>")[0];
  const restContent = displayText.split("</think>")[1] || "";
  // 是否显示光标（仅当流式输出且未结束时显示）
  const showCursor = stream && !end && !stop;

  return (
    <>
      {/* 只添加CSS样式，不修改核心逻辑 */}
      {showCursor && (
        <style dangerouslySetInnerHTML={{
          __html: `
          @keyframes blink-cursor {
            0%, 100% { opacity: 1; }
            50% { opacity: 0; }
          }
         .cursor-blink {
          display: inline-block;
          width: 0.6em;
          height: 0.15em;
          background-color: rgba(0, 0, 0, 0.45);
          margin-left: 0.1em;
          vertical-align: baseline;
          animation: blink-cursor 0.8s step-end infinite;
          position: relative;
          bottom: -0.1em;
        }
        `
        }}/>
      )}

      <ReactMarkdown
        className='markdown-body'
        components={{
          ...components
        }}
        remarkPlugins={[remarkGfm]}
        rehypePlugins={[rehypeRaw]}
      >
        {`
<blockquote class="think">
${thinkContent}${showCursor && thinkContent && !restContent ? '<span class="cursor-blink"></span>' : ''}
</blockquote>
<br/>
${restContent}${showCursor && restContent ? '<span class="cursor-blink"></span>' : ''}
`}
      </ReactMarkdown>
    </>
  )
}

export default memo(StreamTypewriter)
