* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  height: 100vh;
  width: 100vw;
  // overflow: hidden;
}

#root {
  height: 100%;
  overflow-x: hidden;
}


.center {
  margin: 0 auto;
}

// 场景页面 header
.scene-header {
  background-color: #BF0000;
  padding: 0 2rem;
  line-height: 72px;

  // 标题
  .title {
    color: #fff;
    margin: 0;
    font-size: 20px;
    font-weight: bold
  }
}

// 场景页面具体内容
.scene-content {
  padding: 24px;
  flex: 1;

  // 侧边栏
  .sidebar {
    width: 18%;
    height: calc(100vh - 100px);

    // 历史记录列表
    .history-list {
      height: 1000px;
      overflow-y: auto;

      // 历史记录卡片
      .history-item {
        user-select: none;
        background-color: #fff;
        line-height: 28px;
        padding: 0.75rem;
        border-radius: 0.5rem;

        &.active {
          border: 1px solid #BF0000;
        }
      }
    }
  }
}

.custom-scrollbar {
  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb:hover {
    background-color: rgba(255, 255, 255, 0.3);
  }

  &::-webkit-scrollbar-corner {
    background: transparent;
  }
}

/* 容器基本样式 */
.scroll-container {
  overflow: overlay;
  transition: all 0.3s ease;
}

/* 悬停时显示滚动条 */
.scroll-container:hover::-webkit-scrollbar,
.scroll-container:hover::-webkit-scrollbar-track,
.scroll-container:hover::-webkit-scrollbar-thumb {
  visibility: visible;
}

/* Webkit内核浏览器定制滚动条 */
.scroll-container::-webkit-scrollbar {
  width: 8px;
  height: 8px;
  background: transparent;
  visibility: hidden;
}

.scroll-container::-webkit-scrollbar-track {
  background: rgba(200, 200, 200, 0.1);
  border-radius: 4px;
  margin: 4px 0;
  visibility: hidden;
}

.scroll-container::-webkit-scrollbar-thumb {
  background: rgba(150, 150, 150, 0.5);
  border-radius: 4px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  transition: background 0.3s, width 0.2s;
  visibility: hidden;
}

/* 悬停时滑块样式优化 */
.scroll-container:hover::-webkit-scrollbar-thumb {
  background: rgba(120, 120, 120, 0.8);
  background-clip: content-box;
  visibility: visible;
}

/* 横向滚动条优化 */
.scroll-container::-webkit-scrollbar-thumb:horizontal {
  min-width: 40px;
}

/* Firefox浏览器适配 */
@supports (scrollbar-color: red blue) {
  .scroll-container {
    scrollbar-width: thin;
    scrollbar-color: rgba(150, 150, 150, 0.5) transparent;
  }

  .scroll-container:hover {
    scrollbar-color: rgba(120, 120, 120, 0.8) transparent;
  }
}

/* 边缘浏览器适配 */
@supports (-ms-ime-align: auto) {
  .scroll-container {
    -ms-overflow-style: auto;
  }
}

/* 移动端禁用滚动条 */
@media (pointer: coarse) {
  .scroll-container::-webkit-scrollbar {
    display: none;
  }
}

.markdown-body {
  table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    margin: 25px 0;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    box-shadow: 0 2px 15px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    border-radius: 6px;

    thead tr {
      background-color: #BF0000;
      font-size: 16px;
    }
  }

  tr td:nth-child(1) {
    min-width: 120px;
  }

  th {
    color: white;
    font-weight: 600;
    letter-spacing: 0.5px;
    padding: 16px 20px;
    text-transform: uppercase;
    font-size: 0.85em;
    position: relative;
    box-shadow: 0 2px 2px rgba(0, 0, 0, 0.1);
  }

  th:not(:last-child):after {
    content: '';
    position: absolute;
    right: 0;
    top: 20%;
    height: 60%;
    width: 1px;
    background-color: rgba(255, 255, 255, 0.15);
  }

  td {
    padding: 14px 20px;
    color: #333333;
    border-bottom: 1px solid #eee;
    font-size: 0.95em;
    transition: background 0.3s ease;
  }

  tr:last-child td {
    border-bottom: none;
  }

  tbody {
    tr {
      transition: transform 0.2s ease, box-shadow 0.2s ease;

      &:hover {
        background-color: #FFE5E5;
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);

        td {
          color: #8F0000;
        }
      }
    }

    tr:nth-child(even) {
      background-color: rgba(0, 0, 0, 0.02);
    }

    tr:nth-child(odd) {
      background-color: white;
    }
  }


  /* 响应式调整 */
  @media (max-width: 767px) {
    th, td {
      padding: 12px 10px;
      font-size: 0.9em;
    }

    th {
      text-transform: none;
    }
  }
}


.menu-active {
  color: #DF0000;
}

.h-full {
  height: 100%;
}

/* 现代简约滚动条样式 */
::-webkit-scrollbar {
  width: 8px; /* 滚动条宽度 */
  height: 8px; /* 水平滚动条高度 */
}

::-webkit-scrollbar-track {
  background: transparent; /* 轨道背景透明 */
  border-radius: 4px; /* 轨道圆角 */
}

::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2); /* 滑块颜色-浅灰色半透明 */
  border-radius: 4px; /* 滑块圆角 */
  transition: background-color 0.3s; /* 平滑过渡效果 */
}

/* 鼠标悬停时滑块颜色变深 */
::-webkit-scrollbar-thumb:hover {
  background-color: rgba(0, 0, 0, 0.4); /* 悬停时颜色变深 */
}

/* 鼠标按下时滑块颜色 */
::-webkit-scrollbar-thumb:active {
  background-color: rgba(0, 0, 0, 0.5); /* 激活时颜色更深 */
}

/* 确保在Firefox等其他浏览器中也有类似的滚动条样式 */
* {
  scrollbar-width: thin; /* "auto" 或 "thin" */
  scrollbar-color: rgba(0, 0, 0, 0.2) transparent; /* 滑块颜色 轨道颜色 */
}
