
// 基础 URL，请根据实际部署情况修改。如果 OpenAPI spec 中有 servers 定义，应优先使用那里提供的信息。
// 文档中 servers 为空，这里使用相对路径。
const SSO_BASE_URL = import.meta.env.VITE_SSO_BASE_URL || '/ssoserver';

// --- 通用类型定义 ---
interface BaseResponse {
  ret: string; // "0" 表示成功
  msg?: string; // 错误信息
}

// 辅助函数，用于处理API响应和错误 (使用 fetch)
async function handleResponse<T extends BaseResponse>(promise: Promise<Response>): Promise<T> {
  let response: Response;
  try {
    response = await promise;
  } catch (networkError) {
    // 处理网络错误 (例如，DNS 解析失败，无网络连接)
    console.error('API 调用期间发生网络错误:', networkError);
    throw new Error('网络错误，请检查您的连接。');
  }

  let responseData: any;
  try {
    // 尝试解析 JSON，但处理响应可能不是 JSON 的情况 (例如非预期的 HTML 错误页面)
    const contentType = response.headers.get('content-type');
    console.log('response:', response)
    if (contentType && contentType.includes('application/json')) {
      responseData = await response.json();
    } else {
      // 如果不是 JSON，并且不是一个成功的响应 (response.ok 为 false)，则抛出包含状态文本的错误
      if (!response.ok) {
        throw new Error(`API 错误: ${response.status} ${response.statusText || '非JSON错误响应'}`);
      }
      // 如果响应成功 (response.ok 为 true) 但不是 JSON，这对于这些 API 来说是意外的，
      // 但我们会让它通过，以便进行后续的 ret 检查或进一步处理。
      // 或者，如果所有响应都必须是 JSON，您可以在此处抛出错误。
      responseData = { ret: 'unknown', msg: '收到非JSON响应' }; 
    }
  } catch (jsonError) {
    // 处理 JSON 解析错误 (例如，服务器返回格式错误的 JSON，或者响应非预期的非 JSON 内容)
    console.error('解析JSON响应时出错:', jsonError);
    if (!response.ok) { // 如果原始响应也表明了错误状态
        throw new Error(`API 错误: ${response.status} ${response.statusText || '解析错误响应失败'}`);
    }
    throw new Error('从服务器解析JSON响应失败。');
  }

  // 检查 HTTP 错误 (例如，4xx, 5xx)。业务逻辑错误 (data.ret) 的检查在此之后。
  if (!response.ok) {
    // 如果可用，则使用已解析的 JSON 错误中的 ret 和 msg
    const errorMessage = responseData?.msg || '未知的 API 错误';
    const errorCode = responseData?.ret || response.status.toString();
    throw new Error(`API 错误: ${errorMessage} (ret: ${errorCode})`);
  }

  // 检查特定于应用程序的错误 (此时 HTTP 状态码可能为 200，但 'ret' 字段表示错误)
  console.log('responseData:', responseData)
  if (responseData.ret != 0) {
    throw new Error(`API 错误: ${responseData.msg || '未知错误'} (ret: ${responseData.ret})`);
  }

  return responseData as T;
}

// 辅助函数，用于通过 fetch 发送 GET 请求
function fetchGet<T extends BaseResponse>(path: string, params?: Record<string, any>): Promise<T> { // 将 params 类型更改为 Record<string, any>
  const url = new URL(`${SSO_BASE_URL}${path}`);
  if (params) {
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) { // 确保在附加前提供了值
        url.searchParams.append(key, String(value)); // String(value) 处理转换
      }
    });
  }

  const requestOptions: RequestInit = {
    method: 'GET',
    headers: {
      'Content-Type': 'application/json',
      // 如果需要，可在此处添加其他通用请求头，例如 Authorization，但这些 SSO 调用初始似乎不需要它。
    },
  };

  return handleResponse<T>(fetch(url.toString(), requestOptions));
}

// --- API 类型定义和函数实现 ---

// 1. 获取Authorization Code (/ssoserver/moc2/authorize)
export interface GetAuthorizationCodeParams {
  response_type: 'code'; // 授权类型，固定为 'code'
  client_id: string; // SSO申请授权认证的本资源系统编码
  redirect_uri: string; // 成功授权后的回调地址
  state: string; // 客户端状态值，原样返回
  display?: 'mobile'; // 手机类型值固定为 'mobile'，PC端可不填
  logintype?: 'passaccount' | 'certificate' | 'faceid' | 'dynctoken'; // display为'mobile'时需要的登录类型
  username?: string; // display为'mobile'时需要的应用账号
  credential?: string; // display为'mobile'时需要的身份凭证
  mobiletype?: '1' | '2'; // display为'mobile'时需要的设备类型 (1: ios, 2: android)
  deviceid?: string; // display为'mobile'时需要的设备ID
  hostname?: string; // display为'mobile'时需要的主机名
  operateSysVersion?: string; // display为'mobile'时需要的操作系统及版本
  netEnvironment?: string; // display为'mobile'时需要的网络信息 (base64编码)
  address?: string; // display为'mobile'时需要的位置信息
}

export interface AuthorizationCodeResponse extends BaseResponse {
  code?: string; // 授权码
  state?: string; // 客户端的状态值
}

/**
 * 获取临时授权码。
 * 注意：此接口在 display 不为 'mobile' 时通常执行302跳转，由浏览器处理。
 * 当 display='mobile' 时，直接返回JSON。
 * 此函数主要用于 display='mobile' 的场景或需要构造URL的场景。
 * 对于非 mobile 类型的 display，通常您会构造 URL 并重定向浏览器：
 * const url = new URL(`${SSO_BASE_URL}/moc2/authorize`);
 * Object.entries(params).forEach(([key, value]) => url.searchParams.append(key, String(value)));
 * window.location.href = url.toString();
 */
export async function getAuthorizationCode(
  params: GetAuthorizationCodeParams
): Promise<AuthorizationCodeResponse> {
  // 此 fetch 调用主要用于 display='mobile' 且预期返回 JSON 的情况。
  // 对于浏览器重定向，更常见的是直接操作 window.location.href。
  return fetchGet<AuthorizationCodeResponse>('/moc2/authorize', params); // 此处移除了类型断言
}

// 2. 获取Access Token (/ssoserver/moc2/token/get)
export interface GetAccessTokenParams {
  grant_type: 'authorization_code'; // 授权类型，固定为 'authorization_code'
  client_id: string; // SSO申请授权认证的本资源系统编码
  client_secret: string; // client_id 在 oauth2 单点登录配置生成 APPKEY 的 SM3 值，64位大写
  code: string; // 上一步返回的 authorization code
  redirect_uri: string; // 一般与获取 code 时传入的 redirect_uri 保持一致
}

export interface AccessTokenResponse extends BaseResponse {
  access_token?: string; // 授权令牌
  refresh_token?: string; // 用于获取新的 Access_Token 的参数
  expires_in?: string; // access token 的剩余有效期，单位为秒
}

export async function getAccessToken(
  params: GetAccessTokenParams
): Promise<AccessTokenResponse> {
  return fetchGet<AccessTokenResponse>('/moc2/token', params); // 此处移除了类型断言
}

// 3. 更新Access Token (/ssoserver/moc2/token/refresh)
export interface RefreshAccessTokenParams {
  grant_type: 'refresh_token'; // 授权类型，固定为 'refresh_token'
  client_id: string; // SSO申请授权认证的本资源系统编码
  client_secret: string; // client_id 在 oauth2 单点登录配置生成 APPKEY 的 SM3 值，64位大写
  refresh_token: string; // 获取 token 的时候返回的 refresh_token
}

// 响应与 AccessTokenResponse 相同
export async function refreshAccessToken(
  params: RefreshAccessTokenParams
): Promise<AccessTokenResponse> {
  return fetchGet<AccessTokenResponse>('/moc2/token', params); // 此处移除了类型断言
}

// 4. 获取用户OpenID (/ssoserver/moc2/me)
export interface GetUserOpenIdParams {
  access_token: string; // 第二步中获取到的 access token
}

export interface UserOpenIdResponse extends BaseResponse {
  openid?: string; // 生成令牌时登录用户 openid
}

export async function getUserOpenId(
  params: GetUserOpenIdParams
): Promise<UserOpenIdResponse> {
  return fetchGet<UserOpenIdResponse>('/moc2/me', params); // 此处移除了类型断言
}

// 5. 获取用户信息 (/ssoserver/user/info)
export interface GetUserInfoParams {
  access_token: string; // 已经颁发的令牌
  oauth_consumer_key: string; // SSO申请授权认证的本资源系统id (即 client_id)
  openid: string; // 第三步返回的 openid
}

interface UserInfo {
  loginname?: string; // 用户登录名
  username?: string; // 用户姓名 (可选)
  email?: string; // 用户邮箱 (可选)
  mobile?: string; // 手机号 (可选)
  deptcode?: string; // 部门编码 (可选)
  headship?: string; // 用户职务 (可选)
  description?: string; // 用户描述 (可选)
  customAttr?: string; // 扩展属性值 (可选)
  telphone?: string; // 座机号码 (可选)
  userstatu?: string; // 用户状态：1启用 0禁用 (可选)
  timestamp?: string; // 账号最后修改时间 (可选)
  resaccounts?: string; // 业务系统账号 (可选)
  role?: string; // 角色 (可选)
  post?: string; // 岗位 (可选)
}

export interface UserInfoResponse extends BaseResponse {
  userinfo?: UserInfo; // 用户信息
}

export async function getUserInfo(
  params: GetUserInfoParams
): Promise<UserInfoResponse> {
  return fetchGet<UserInfoResponse>('/user/info', params); // 此处移除了类型断言
}

// 建议：可以导出一个包含所有API方法的对象
export const ssoApi = {
  getAuthorizationCode,
  getAccessToken,
  refreshAccessToken,
  getUserOpenId,
  getUserInfo,
}; 