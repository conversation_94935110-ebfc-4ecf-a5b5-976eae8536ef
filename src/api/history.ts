const AI_BASE = import.meta.env["VITE_AI_API_BASE"] || "";

export async function getConversations(user: string, key: string, last_id?: string, limit = 20) {
  let params = `${AI_BASE}/conversations?user=${user}&limit=${limit}`;
  if (last_id) {
    params += `&last_id=${last_id}`;
  } else {
    params += `&last_id=`;
  }
  const result = await fetch(params, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${key}`,
    }
  }).then(res => res.json());
  return (await Promise.allSettled(result.data.map(async (item: any) => {
    const messages = await getMessages(user, key, item.id, 1);
    item.query = messages.data[0].query
    item.answer = messages.data[0].answer
    return item;
  }))).map((item) => item.status === 'fulfilled' ? item.value : null).filter(Boolean);
}


export function getMessages(user: string, key: string, conversation_id: string, limit = 20) {
  const params = `${AI_BASE}/messages?user=${user}&conversation_id=${conversation_id}&limit=${limit}`;
  return fetch(`${params}`, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${key}`,
    }
  }).then(res => res.json());
}

export async function deleteConversation(user: string, key: string, conversation_id: string) {
  const params = `${AI_BASE}/conversations/${conversation_id}`;
  const response = await fetch(params, {
    method: "DELETE",
    headers: {
      "Content-Type": "application/json",
      "Authorization": `Bearer ${key}`,
    },
    body: JSON.stringify({
      user: user
    })
  });
  
  // 检查响应状态
  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }
  
  // 检查响应内容
  const text = await response.text();
  
  // 如果响应为空，认为删除成功
  if (!text.trim()) {
    return { result: 'success' };
  }
  
  // 尝试解析JSON
  try {
    return JSON.parse(text);
  } catch (error) {
    // 如果不是JSON格式，但状态码是成功的，认为删除成功
    return { result: 'success' };
  }
}
