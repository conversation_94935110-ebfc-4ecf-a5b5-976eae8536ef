const DIFY_API_BASE = import.meta.env["VITE_API_DIFY_CONSOLE"] || "";

export function difyLogin(email: string, password: string) {
  return fetch(`${DIFY_API_BASE}/login`, {
    method: 'POST',
    headers: {
      "Content-Type": "application/json"
    },
    body: JSON.stringify({
      email: email,
      password: password,
      language: "zh-Hans",
      remember_me: true
    })
  });
}

export function difyProfile() {
  try {
    const userAuth = JSON.parse(localStorage.getItem("user") || "{}");
    const token = userAuth?.data?.access_token || "";

    if (!token) {
      throw new Error("无访问令牌");
    }

    return fetch(`${DIFY_API_BASE}/account/profile`, {
      method: 'GET',
      headers: {
        "Content-Type": "application/json",
        "Authorization": `Bearer ${token}`
      }
    });
  } catch (error) {
    console.error("获取配置文件时出错:", error);
    // 返回一个会自动失败的 Promise
    return Promise.resolve({
      ok: false,
      status: 401,
      json: () => Promise.resolve({error: "认证失败"})
    } as Response);
  }
}
