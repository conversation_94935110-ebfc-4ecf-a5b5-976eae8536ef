import {SSEClient, SSERequest} from "@/utils/sse-client.ts";


export async function chat(user: string, key: string, query: string, inputs: any, files: any, {
  onStart,
  onMessage,
  onError,
  onFinish
}: Pick<SSERequest, "onStart" | "onMessage" | "onError" | "onFinish">, conversationId ?: string) {
  const client = new SSEClient(key);
  return client.sendMessage({
    type: "chat",
    query,
    conversationId: conversationId,
    user,
    inputs,
    files,
    onStart,
    onMessage,
    onError,
    onFinish
  });
}

