export interface OALoginRequest {
  username: string;
  password: string;
}

export interface OALoginResponse {
  code: number;
  message: string;
  data: {
    token: string;
    username: string;
    expiresIn: number;
    menus: any[];
  };
}

export interface OALoginResult {
  token: string;
  username: string;
}

const OA_API_BASE = import.meta.env["VITE_OA_BASE_URL"] || "";

export async function oaLogin(username: string, password: string): Promise<OALoginResult> {
  const response = await fetch(`/oa/api/auth/login`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      username,
      password
    })
  });

  if (!response.ok) {
    throw new Error(`登录失败: ${response.status}`);
  }

  const result: OALoginResponse = await response.json();
  
  if (result.code !== 200) {
    throw new Error(result.message || '登录失败');
  }

  return {
    token: result.data.token,
    username: result.data.username
  };
} 