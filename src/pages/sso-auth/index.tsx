import React, { useEffect } from 'react';
import { Spin } from 'antd';
import { useNavigate } from 'react-router-dom';

// 统一在此处定义从环境变量读取的配置
const SSO_ENABLED = import.meta.env.VITE_SSO_ENABLED === 'true';
const SSO_BASE_URL = import.meta.env.VITE_SSO_BASE_URL || '/ssoserver';
const SSO_CLIENT_ID = import.meta.env.VITE_SSO_CLIENT_ID || 'YOUR_SSO_CLIENT_ID';
const SSO_REDIRECT_URI = window.location.origin + '/#/auth/sso-callback';

const RedirectPage: React.FC = () => {
  const navigate = useNavigate();

  useEffect(() => {
    if (SSO_ENABLED) {
      // SSO登录启用，构建SSO授权URL并跳转
      // 1. 生成并存储 state 参数 (用于CSRF保护)
      const state = '1234567890'; // 在生产环境应使用更安全的随机值
      sessionStorage.setItem('sso_state', state);

      // 2. 构建SSO授权URL
      const authorizeUrl = new URL(`${SSO_BASE_URL}/moc2/authorize`);
      
      authorizeUrl.searchParams.append('response_type', 'code');
      authorizeUrl.searchParams.append('client_id', SSO_CLIENT_ID);
      authorizeUrl.searchParams.append('state', state);
      authorizeUrl.searchParams.append('redirect_uri', SSO_REDIRECT_URI);
      
      console.log('重定向到SSO授权页面:', authorizeUrl.toString());
      
      // 3. 重定向到SSO授权页面
      window.location.href = authorizeUrl.toString();
    } else {
      // SSO登录未启用，跳转到普通登录页面
      navigate('/login');
    }
  }, [navigate]);

  // 显示加载状态，因为即将发生重定向
  return (
    <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
      <Spin size="large" tip="正在跳转到登录页面..." />
    </div>
  );
};

export default RedirectPage; 