import React, { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { message, Spin, Result, Button } from 'antd';
import { useAuth, login } from '../../contexts/auth-context';
import * as ssoApi from '../../api/sso';

// 从环境变量读取配置，提供默认值以防万一（生产环境应确保已配置）
const SSO_CLIENT_ID = import.meta.env.VITE_SSO_CLIENT_ID || 'YOUR_CLIENT_ID';
const SSO_CLIENT_SECRET = import.meta.env.VITE_SSO_CLIENT_SECRET || 'YOUR_CLIENT_SECRET';
// const SSO_REDIRECT_URI = window.location.origin + '%2F%23%2Fauth%2Fsso-callback';
const SSO_REDIRECT_URI = window.location.origin + '/#/auth/sso-callback';

const SsoCallbackPage: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { login } = useAuth();

  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const processSsoCallback = async () => {
      if (SSO_CLIENT_ID === 'YOUR_CLIENT_ID' || SSO_CLIENT_SECRET === 'YOUR_CLIENT_SECRET') {
        setError('SSO客户端配置 (Client ID 或 Client Secret) 缺失，请检查环境变量。');
        setIsLoading(false);
        console.error('SSO 客户端ID或客户端密钥未配置。');
        return;
      }

      const searchParams = new URLSearchParams(location.search);
      const code = searchParams.get('code');
      const stateFromUrl = searchParams.get('state');

      if (!code) {
        setError('授权码 (code) 未找到。');
        setIsLoading(false);
        return;
      }

      // 针对 IdP 发起的 SSO 调整 state 验证逻辑
      // if (stateFromUrl) {
      //   const savedState = sessionStorage.getItem('sso_state');
      //   sessionStorage.removeItem('sso_state'); 
      //   if (stateFromUrl !== savedState) {
      //     setError('状态 (state) 验证失败，请重新尝试登录以确保安全。');
      //     setIsLoading(false);
      //     return;
      //   }
      //   console.log('SSO回调：SP发起的流程，state 验证成功。');
      // } else {
      //   console.warn('SSO回调：URL中未找到 state 参数，假定为 IdP 发起的流程。将继续尝试使用 code。');
      //   sessionStorage.removeItem('sso_state'); 
      // }
      sessionStorage.removeItem('sso_state'); 

      try {
        console.log('SSO 开始获取 Access Token...');
        const tokenResponse = await ssoApi.getAccessToken({
          grant_type: 'authorization_code',
          client_id: SSO_CLIENT_ID,
          client_secret: SSO_CLIENT_SECRET,
          code: code,
          redirect_uri: SSO_REDIRECT_URI,
        });
        const accessToken = tokenResponse.access_token;
        const ssoRefreshToken = tokenResponse.refresh_token; // 获取refresh_token (如果SSO提供商返回)

        if (!accessToken) {
          throw new Error('未能获取到 Access Token。响应: ' + JSON.stringify(tokenResponse));
        }

        const loginResult = await login("","","sso",accessToken,ssoRefreshToken);

        if (loginResult.result === "success") {
          message.success("登录成功，欢迎回来");
          navigate("/employee-knowledge-answer");
        } else {
          setError(loginResult.message || '通过 SSO 获取信息成功，但在应用内登录失败。请检查控制台获取更多信息或联系管理员。');
          console.error('SSO 登录成功但 ssologin 失败。');
        }

      } catch (err) {
        console.error('SSO 回调处理失败:', err);
        let errMsg = 'SSO 登录过程中发生错误。';
        if (err instanceof Error) {
          errMsg = err.message;
        }
        setError(errMsg);
      } finally {
        setIsLoading(false);
      }
    };

    processSsoCallback();
  }, [location, navigate]);

  // 加载状态UI
  if (isLoading) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
        <Spin size="large" tip="正在处理SSO登录，请稍候..." />
      </div>
    );
  }

  // 错误状态UI
  if (error) {
    return (
      <Result
        status="error"
        title="SSO 登录失败"
        subTitle={error}
        extra={[
          <Button type="primary" key="backToLogin" onClick={() => navigate('/login', { replace: true })}>
            返回登录页
          </Button>,
        ]}
      />
    );
  }

  // 正常情况下，用户不应该看到这个UI，因为会重定向或显示错误
  return (
    <Result
      status="success"
      title="SSO 处理已完成"
      subTitle="如果未自动跳转，请手动刷新或返回登录页。"
    />
  );
};

export default SsoCallbackPage; 