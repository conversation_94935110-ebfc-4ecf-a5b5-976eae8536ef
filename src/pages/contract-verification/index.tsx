import {useState} from "react";

import {Flex, Layout, message, Steps} from 'antd'
import ContractVerificationStep1 from "@/pages/contract-verification/components/step1";
import ContractVerificationStep2 from "@/pages/contract-verification/components/step2.tsx";
import ContractVerificationStep3 from "@/pages/contract-verification/components/step3.tsx";

const {Content} = Layout

export const ContractVerification = () => {
  // 公共状态
  const [messageApi, contextHolder] = message.useMessage()
  const [conversationId, setConversationId] = useState<string>("")
  const [uploadedFile, setUploadedFile] = useState<{ id: string, name: string }>();
  const [currentStep, setCurrentStep] = useState<number>(0);
  const [rules, setRules] = useState<string>("");

  const steps = [{
    title: '上传合同文档',
    content: <ContractVerificationStep1
      uploadedFile={uploadedFile}
      setUploadedFile={setUploadedFile}
      setCurrent={setCurrentStep}
      messageApi={messageApi}/>,
  },
    {
      title: '提取规则',
      content: <ContractVerificationStep2
        uploadedFile={uploadedFile}
        setConversationId={setConversationId}
        setCurrent={setCurrentStep}
        setRules={setRules}/>,
    },
    {
      title: '内容校验',
      content: <ContractVerificationStep3
        uploadedFile={uploadedFile}
        conversationId={conversationId}
        setConversationId={setConversationId}
        setCurrent={setCurrentStep}
        rules={rules}/>,
    },]

  return (
    <>
      {contextHolder}
      <Content className="scene-content">
        <Flex gap="middle">
          <Flex vertical justify='space-between' style={{flex: "1"}}>
            <Flex vertical
                  style={{
                    height: 'calc(100vh - 120px)',
                    padding: "1rem",
                    backgroundColor: "#fff",
                    borderRadius: "5px",
                    marginBottom: '16px',
                    overflow: 'auto',
                    scrollBehavior: 'smooth'
                  }}
            >
              <Steps style={{marginBottom: "2rem", userSelect: "none"}} current={currentStep} items={steps.map(i => {
                return {title: i.title}
              })}/>
              {steps[currentStep].content}
            </Flex>
          </Flex>
        </Flex>
      </Content>
    </>
  )
}

export default ContractVerification
