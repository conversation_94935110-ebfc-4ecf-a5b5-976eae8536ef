import {Button, Flex, Space, message} from "antd";
import {chat} from "@/api/chat.ts";
import {useEffect, useState} from "react";
import {useAuth} from "@/contexts/auth-context.tsx";
import StreamTypewriter from "@/component/StreamTypewriter";
import {CustomAvatar} from "@/component/custom-avatar";

const appKey = import.meta.env["VITE_CONTRACT_VERI_KEY"] || ""

interface ContractVerificationStep3Props {
  uploadedFile: { id: string; name: string };
  conversationId: string;
  setConversationId: (id: string) => void;
  setCurrent: (step: number) => void;
  rules: string;
}

const ContractVerificationStep3 = ({
                                     uploadedFile,
                                     conversationId,
                                     setConversationId,
                                     setCurrent,
                                     rules
                                   }: ContractVerificationStep3Props) => {
  const [generating, setGenerating] = useState<boolean>(false)
  const [downloading, setDownloading] = useState<boolean>(false)
  const [responsePrompt, setResponsePrompt] = useState<string>("正在唤醒AI...")
  const [messages, setMessages] = useState<string>('')
  const {user} = useAuth()
  const [messageApi, contextHolder] = message.useMessage()

  useEffect(() => {
    setMessages("");
    handleVerification()
  }, [uploadedFile, conversationId])

  /** 规则校验 */
  const handleVerification = async () => {
    if (!uploadedFile) {
      return;
    }
    setGenerating(true);

    try {
      await chat(user!.email, appKey, `请校验该合同：【${uploadedFile.name}】`, {type: "内容校验", rules}, [{
        type: "document",
        transfer_method: "local_file",
        upload_file_id: uploadedFile.id
      }], {
        onStart: () => {
          setResponsePrompt("正在进行规则校验")
        },
        onMessage: (text, finished, conversationId) => {
          if (text) {
            setMessages(prev => prev + text)
          }
          if (finished && conversationId) {
            setGenerating(false)
          }
        },
        onError: () => {
          setGenerating(false)
        },
        onFinish: () => {
        }
      }, conversationId).then(() => {
      })
    } catch {
      setGenerating(false)
    }
  }

  /** 下载批注版文档 */
  const handleDownloadAnnotatedDocument = async () => {
    if (!uploadedFile || downloading) {
      return;
    }

    setDownloading(true);
    messageApi.open({
      key: 'downloading',
      type: 'loading',
      content: '正在生成批注版文档...',
      duration: 0
    });

    try {
      // 过滤掉think标签内容，只保留实际的校验结果
      const filteredResults = messages.replace(/<think>[\s\S]*?<\/think>/g, '').trim();
      
      // 将过滤后的校验结果包含在query中
      const queryWithResults = `请校批注合同：【${uploadedFile.name}】\n\n基于以下校验结果进行批注：\n${filteredResults}`;
      
      await chat(user!.email, appKey, queryWithResults, {type: "合同批注", rules: filteredResults}, [{
        type: "document",
        transfer_method: "local_file",
        upload_file_id: uploadedFile.id
      }], {
        onStart: () => {
          messageApi.open({
            key: 'downloading',
            type: 'loading',
            content: '正在生成批注版文档...',
            duration: 0
          });
        },
        onMessage: (text, finished, conversationId) => {
          if (finished && conversationId) {
            setDownloading(false);
          }
        },
        onError: () => {
          setDownloading(false);
          messageApi.open({
            key: 'downloading',
            type: 'error',
            content: '批注文档生成失败，请重试',
            duration: 3
          });
        },
        onFinish: (data: any) => {
          // 处理下载逻辑 - 根据实际API响应结构
          if (data?.files && data.files.length > 0) {
            const file = data.files[0];
            const relativeUrl = file.url;
            const originalFileName = file.filename;
            
            // 拼接完整的下载URL
            const downloadUrl = `${import.meta.env["VITE_AI_DIFY_DOWNLOAD"]}${relativeUrl}`;
            
            // 生成下载文件名，如果原文件名不包含"批注版"则添加
            const fileName = originalFileName.includes('批注') 
              ? originalFileName 
              : `${uploadedFile.name.replace(/\.(docx|doc)$/i, '')}_批注版.docx`;
            
            // 直接使用拼接后的完整URL进行下载
            fetch(downloadUrl, {
              method: 'GET',
            })
            .then(response => {
              if (!response.ok) {
                throw new Error(`下载失败: ${response.status}`);
              }
              return response.blob();
            })
            .then(blob => {
              // 创建下载链接
              const url = window.URL.createObjectURL(blob);
              const link = document.createElement('a');
              link.href = url;
              link.download = fileName;
              link.style.display = 'none';
              
              document.body.appendChild(link);
              link.click();
              document.body.removeChild(link);
              window.URL.revokeObjectURL(url);
              
              messageApi.open({
                key: 'downloading',
                type: 'success',
                content: '批注版文档下载成功',
                duration: 3
              });
            })
            .catch(error => {
              console.error('下载失败:', error);
              messageApi.open({
                key: 'downloading',
                type: 'error',
                content: '文档下载失败，请重试',
                duration: 3
              });
            });
          } else {
            messageApi.open({
              key: 'downloading',
              type: 'error',
              content: '未获取到批注文档，请重试',
              duration: 3
            });
          }
          setDownloading(false);
        }
      }, conversationId).then(() => {
      })
    } catch (error) {
      setDownloading(false);
      messageApi.open({
        key: 'downloading',
        type: 'error',
        content: '批注文档生成失败，请重试',
        duration: 3
      });
    }
  }

  return (
    <>
      {contextHolder}
      <Flex vertical justify='space-between' style={{height: 'calc(100vh - 140px)', flex: "1"}}>
        <Flex vertical
              style={{
                padding: "1rem",
                backgroundColor: "#fff",
                borderRadius: "5px",
                marginBottom: '16px',
                height: '75vh',
                overflow: 'auto',
                scrollBehavior: 'smooth'
              }}
        >
          {
            <Space direction="vertical" style={{color: 'rgba(0, 0, 0, 0.45)'}}>
              <Space size="middle" align="start" style={{margin: "0.5rem auto"}}>
                <CustomAvatar color="#BF0000"/>
                {
                  !messages && <p>{responsePrompt}</p>
                }
                {messages &&
                  <StreamTypewriter text={messages} end={!generating}/>}
              </Space>
            </Space>
          }
        </Flex>
        <Flex style={{flex: 1, margin: '0 auto'}} gap="large">
          <Button 
            type="primary" 
            loading={downloading}
            disabled={generating || uploadedFile.name.endsWith(".pdf") || downloading} 
            onClick={handleDownloadAnnotatedDocument}
          >
            {downloading ? '生成中...' : '下载批注版文档'} {uploadedFile.name.endsWith(".pdf") && "（仅支持docx文件）"}
          </Button>
          <Button disabled={generating || downloading} onClick={() => {
            setCurrent(0)
            setConversationId("")
          }}>重新上传合同</Button>
        </Flex>
      </Flex>
    </>
  )
}

export default ContractVerificationStep3;
