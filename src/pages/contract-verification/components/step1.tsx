import ReactMarkdown from "react-markdown";
import {But<PERSON>, Flex, Upload} from "antd";
import styles from "@/pages/contract-verification/index.module.less";
import {InboxOutlined} from "@ant-design/icons";
import {uploadFile} from "@/api/file.ts";

const appKey = import.meta.env["VITE_CONTRACT_VERI_KEY"] || ""
const prologue: string = `
### 合同校验助手

您好，我是诚通证券合同校验助手，我能够帮您做以下事情：
1. 上传合同文档，自动与内部合同库及相关法律法规进行深度比对分析；
2. 快速识别合同条款中的潜在法律风险点；
3. 确保合同条款符合最新监管要求。

请您将合同文件上传到下方区域，并在上传成功后根据指引逐步完成工作。

我还在不停的优化中，如您有需求，可以随时向我提供，谢谢您的支持！
`

const ContractVerificationStep1 = ({
                                     uploadedFile,
                                     setUploadedFile,
                                     setCurrent,
                                     messageApi
                                   }) => {
  const beforeUpload = (file: File) => {
    const originalFileExt = file.name.substring(file.name.lastIndexOf('.') + 1)
    if (['docx', 'pdf'].includes(originalFileExt)) {
      messageApi.open({
        key: 'uploading',
        type: 'loading',
        content: '文件上传中'
      })
      uploadFile(file, appKey).then(async response => {
        if (response.id) {
          setUploadedFile(response)
          messageApi.open({
            key: 'uploading',
            type: 'success',
            content: '文件上传成功',
            duration: 1
          })
        } else {
          messageApi.open({
            key: 'uploading',
            type: 'error',
            content: '文件上传失败',
            duration: 1
          })
        }
      })
    }
  }
  /** 匹配规则 */
  const handleGenerateRules = async () => {
    if (!uploadedFile) {
      return;
    }
    setCurrent(1);
    return;
  }

  return <Flex vertical justify='space-between' style={{height: 'calc(100vh - 100px)', flex: "1"}}>
    <Flex vertical
          style={{
            padding: "1rem",
            backgroundColor: "#fff",
            borderRadius: "5px",
            marginBottom: '16px',
            height: '80vh',
            overflow: 'auto',
            scrollBehavior: 'smooth'
          }}
    >
      <ReactMarkdown className='main-content center'>{prologue}</ReactMarkdown>
      <Upload.Dragger className={styles["scene-input"]} showUploadList={false} multiple={false}
                      beforeUpload={beforeUpload} accept='.docx,.pdf'>
        <p className="ant-upload-drag-icon">
          <InboxOutlined/>
        </p>
        {
          uploadedFile ? <>
              <p className="ant-upload-text">{uploadedFile.name}</p>
              <p className="ant-upload-hint">点击或拖拽文件到此处重新进行上传</p>
            </> :
            <>
              <p className="ant-upload-text">请点击或拖拽上传合同文件</p>
              <p className="ant-upload-hint">暂支持.docx，.pdf（非扫描文件）</p>
            </>
        }
      </Upload.Dragger>
      {uploadedFile && <Button type="primary" className={styles["scene-input"]}
                               onClick={handleGenerateRules}>开始解析</Button>}
    </Flex>
  </Flex>
}

export default ContractVerificationStep1;
