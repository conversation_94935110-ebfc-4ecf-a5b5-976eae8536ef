import {But<PERSON>, <PERSON>lex, Space} from "antd";
import {chat} from "@/api/chat.ts";
import {useEffect, useState} from "react";
import {useAuth} from "@/contexts/auth-context.tsx";
import StreamTypewriter from "@/component/StreamTypewriter";
import {CustomAvatar} from "@/component/custom-avatar";

const appKey = import.meta.env["VITE_CONTRACT_VERI_KEY"] || ""


const ContractVerificationStep2 = ({
                                     uploadedFile,
                                     setConversationId,
                                     setCurrent,
                                     setRules,
                                   }) => {
  const [generating, setGenerating] = useState<boolean>(false)
  const [responsePrompt, setResponsePrompt] = useState<string>("正在唤醒AI...")
  const [messages, setMessages] = useState<string>('')
  const {user} = useAuth()

  useEffect(() => {
    setMessages("");
    handleGenerateRules()
  }, [uploadedFile])

  /** 匹配规则 */
  const handleGenerateRules = async () => {
    if (!uploadedFile) {
      return;
    }
    setGenerating(true);

    try {
      await chat(user!.email, appKey, `请解析该合同：【${uploadedFile.name}】`, {type: "匹配规则"}, [{
        type: "document",
        transfer_method: "local_file",
        upload_file_id: uploadedFile.id
      }], {
        onStart: () => {
          setResponsePrompt("正在匹配规则")
        },
        onMessage: (text, finished, conversationId) => {
          if (text) {
            setMessages(prev => prev + text)
          }
          if (finished && conversationId) {
            setGenerating(false)
            setConversationId(conversationId)
          }
        },
        onError: () => {
          setGenerating(false)
        },
        onFinish: () => {
        }
      }, "").then(() => {
      })
    } catch {
      setGenerating(false)
    }
  }

  return <Flex vertical justify='space-between' style={{height: 'calc(100vh - 140px)', flex: "1"}}>
    <Flex vertical
          style={{
            padding: "1rem",
            backgroundColor: "#fff",
            borderRadius: "5px",
            marginBottom: '16px',
            height: '75vh',
            overflow: 'auto',
            scrollBehavior: 'smooth'
          }}
    >
      {
        <Space direction="vertical" style={{color: 'rgba(0, 0, 0, 0.45)'}}>
          <Space size="middle" align="start" style={{margin: "0.5rem auto"}}>
            <CustomAvatar color="#BF0000"/>
            {
              !messages && <p>{responsePrompt}</p>
            }
            {messages &&
              <StreamTypewriter speed={1} text={`为您匹配到以下合同校验规则：</think>\n\n` + messages}
                                end={!generating}/>}
          </Space>
        </Space>
      }
    </Flex>
    <Flex style={{flex: 1, margin: '0 auto'}} gap="large">
      <Button type="primary" disabled={generating} onClick={() => {
        setCurrent(2);
        setRules(messages)
      }}>进行规则校验</Button>
      <Button disabled={generating} onClick={() => setCurrent(0)}>重新上传合同</Button>
    </Flex>
  </Flex>
}

export default ContractVerificationStep2;
