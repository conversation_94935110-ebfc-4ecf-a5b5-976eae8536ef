import {useState} from "react";
import {Button, Card, Form, Input, Layout, message, Typography, Divider} from "antd";
import {LockOutlined, UserOutlined, ApartmentOutlined} from "@ant-design/icons";
import {useLocation, useNavigate} from "react-router-dom";
import {useAuth} from "../../contexts/auth-context";
import "./index.less";

// 统一在此处定义从环境变量读取的配置
const SSO_BASE_URL_CONFIG = import.meta.env.VITE_SSO_BASE_URL || '/ssoserver';
const SSO_CLIENT_ID_CONFIG = import.meta.env.VITE_SSO_CLIENT_ID || 'YOUR_SSO_CLIENT_ID';
const OA_ENABLED = import.meta.env.VITE_OA_ENABLED === 'true';
// 建议回调URL也做成可配置的，或者基于当前域名生成
// const SSO_REDIRECT_URI_CONFIG = window.location.origin + '%2F%23%2Fauth%2Fsso-callback';
const SSO_REDIRECT_URI_CONFIG = window.location.origin + '/#/auth/sso-callback';

const {Content} = Layout;
const {Title, Text} = Typography;

const Login = () => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const {login} = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  // 提取重定向URL
  const searchParams = new URLSearchParams(location.search);
  const redirectUrl = searchParams.get("redirect") || "/employee-knowledge-answer";

  const handleSubmit = async (values: { email: string; password: string; }) => {
    const {email, password} = values;
    setLoading(true);

    try {
      if (OA_ENABLED) {
        await login(email, password, "oa", "", "");
        message.success("OA登录成功，欢迎回来");
      } else {
        await login(email, password, "dify", "", "");
        message.success("登录成功，欢迎回来");
      }
      navigate("/employee-knowledge-answer");
    } catch (err) {
      if (err instanceof Error) {
        message.error(err.message);
      } else {
        message.error(OA_ENABLED ? "OA登录失败，请检查用户名和密码是否正确" : "登录失败，请检查邮箱和密码是否正确");
      }
    } finally {
      setLoading(false);
    }
  };

  const handleSsoLogin = () => {
    if (SSO_CLIENT_ID_CONFIG === 'YOUR_SSO_CLIENT_ID') {
      message.error('SSO Client ID 未配置，请检查环境变量 VITE_SSO_CLIENT_ID');
      console.error('SSO Client ID is not configured. Please set VITE_SSO_CLIENT_ID in your environment variables.');
      return;
    }

    // 1. 生成并存储 state 参数 (用于CSRF保护)
    const state = '1234567890'; //crypto.randomUUID(); // 使用 crypto.randomUUID() 生成随机状态值
    sessionStorage.setItem('sso_state', state);

    // 2. 构建SSO授权URL
    const authorizeUrl = new URL(`${SSO_BASE_URL_CONFIG}/moc2/authorize`);
    
    
    authorizeUrl.searchParams.append('response_type', 'code');
    authorizeUrl.searchParams.append('client_id', SSO_CLIENT_ID_CONFIG);
    authorizeUrl.searchParams.append('state', state);
    authorizeUrl.searchParams.append('redirect_uri', SSO_REDIRECT_URI_CONFIG);
    // 根据需要可以添加其他参数，如 display, logintype 等，参考 GetAuthorizationCodeParams
    // authorizeUrl.searchParams.append('display', 'mobile'); 
    console.log('authorizeUrl:', authorizeUrl.toString())
    // debugger;
    // 3. 重定向到SSO授权页面
    window.location.href = authorizeUrl.toString();
  };

  return (
    <Layout
      className="layout"
      style={{
        minHeight: "100vh",
        position: "relative",
        overflow: "hidden",
        background: "linear-gradient(120deg, #f0f2f5 80%, #e6f7ff 100%)"
      }}
    >
      <div style={{position: "absolute", bottom: "0", left: "0", width: "100%", height: "40%", overflow: "hidden"}}>
        <svg
          viewBox="0 0 1440 320"
          preserveAspectRatio="none"
          style={{position: "absolute", bottom: 0, width: "100%", height: "100%"}}
        >
          <defs>
            <linearGradient id="waveGradient" x1="0%" y1="0%" x2="100%" y2="0%">
              <stop offset="0%" style={{stopColor: "#BF0000", stopOpacity: "0.05"}}/>
              <stop offset="100%" style={{stopColor: "#D40000", stopOpacity: "0.05"}}/>
            </linearGradient>
            <linearGradient id="waveGradient2" x1="0%" y1="0%" x2="100%" y2="0%">
              <stop offset="0%" style={{stopColor: "#BF0000", stopOpacity: "0.1"}}/>
              <stop offset="100%" style={{stopColor: "#D40000", stopOpacity: "0.1"}}/>
            </linearGradient>
          </defs>
          <path
            fill="url(#waveGradient)"
            d="M0,32L48,42.7C96,53,192,75,288,85.3C384,96,480,96,576,106.7C672,117,768,139,864,160C960,181,1056,192,1152,186.7C1248,181,1344,160,1392,149.3L1440,139L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"
          >
            <animate
              attributeName="d"
              dur="10s"
              repeatCount="indefinite"
              values="
        M0,32L48,42.7C96,53,192,75,288,85.3C384,96,480,96,576,106.7C672,117,768,139,864,160C960,181,1056,192,1152,186.7C1248,181,1344,160,1392,149.3L1440,139L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z;

        M0,16L48,26.7C96,37,192,59,288,69.3C384,80,480,80,576,90.7C672,101,768,123,864,144C960,165,1056,176,1152,170.7C1248,165,1344,144,1392,133.3L1440,123L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z;

        M0,32L48,42.7C96,53,192,75,288,85.3C384,96,480,96,576,106.7C672,117,768,139,864,160C960,181,1056,192,1152,186.7C1248,181,1344,160,1392,149.3L1440,139L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"
            />
          </path>
          <path
            fill="url(#waveGradient2)"
            d="M0,64L48,74.7C96,85,192,107,288,112C384,117,480,107,576,117.3C672,128,768,160,864,176C960,192,1056,192,1152,181.3C1248,171,1344,149,1392,138.7L1440,128L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"
          >
            <animate
              attributeName="d"
              dur="15s"
              repeatCount="indefinite"
              values="
        M0,64L48,74.7C96,85,192,107,288,112C384,117,480,107,576,117.3C672,128,768,160,864,176C960,192,1056,192,1152,181.3C1248,171,1344,149,1392,138.7L1440,128L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z;

        M0,48L48,58.7C96,69,192,91,288,96C384,101,480,91,576,101.3C672,112,768,144,864,160C960,176,1056,176,1152,165.3C1248,155,1344,133,1392,122.7L1440,112L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z;

        M0,64L48,74.7C96,85,192,107,288,112C384,117,480,107,576,117.3C672,128,768,160,864,176C960,192,1056,192,1152,181.3C1248,171,1344,149,1392,138.7L1440,128L1440,320L1392,320C1344,320,1248,320,1152,320C1056,320,960,320,864,320C768,320,672,320,576,320C480,320,384,320,288,320C192,320,96,320,48,320L0,320Z"
            />
          </path>
        </svg>
      </div>

      <Content
        style={{
          display: "flex",
          justifyContent: "flex-end", // Changed from "center" to "flex-end"
          paddingRight: "10%", // Add some padding from the right edge
          alignItems: "center",
          padding: "50px 16px",
          position: "relative",
          marginRight: "250px",
          marginBottom: "100px",
          zIndex: 1
        }}
      >
        <Card
          style={{
            width: 480,
            boxShadow: "0 8px 24px rgba(191, 0, 0, 0.15)"
          }}
          bodyStyle={{padding: "40px"}}
        >
          <div style={{textAlign: "center", marginBottom: 32, userSelect: "none"}}>
            <img draggable={false} className="logo" src="/logo.png" alt={"北方信托 Logo"}/>
            <Title level={2} style={{marginBottom: "0.75em"}}>
              
            </Title>
            <Text type="secondary" style={{fontSize: "16px"}}>
              请输入您的账号信息进行登录
            </Text>
          </div>

          <Form
            form={form}
            name="login"
            onFinish={handleSubmit}
            autoComplete="off"
            layout="vertical"
            size="large"
          >
            <Form.Item
              name="email"
              rules={OA_ENABLED ? [
                {required: true, message: "用户名不能为空"}
              ] : [
                {required: true, message: "邮箱不能为空"},
                {type: "email", message: "请输入有效的邮箱地址"}
              ]}
            >
              <Input
                prefix={<UserOutlined style={{color: "#d9d9d9"}}/>}
                placeholder={OA_ENABLED ? "请输入用户名" : "请输入邮箱地址"}
                style={{borderRadius: "6px", height: "48px"}}
              />
            </Form.Item>

            <Form.Item
              name="password"
              rules={[{required: true, message: "密码不能为空"}]}
            >
              <Input.Password
                prefix={<LockOutlined style={{color: "#d9d9d9"}}/>}
                placeholder="请输入密码"
                style={{borderRadius: "6px", height: "48px"}}
              />
            </Form.Item>

            <Form.Item>
              <Button
                type="primary"
                htmlType="submit"
                loading={loading}
                block
                style={{
                  height: "48px",
                  borderRadius: "6px",
                  fontSize: "16px",
                  fontWeight: "500",
                  backgroundColor: "#BF0000",
                  borderColor: "#BF0000"
                }}
                onMouseOver={(e) => e.currentTarget.style.backgroundColor = "#D40000"}
                onMouseOut={(e) => e.currentTarget.style.backgroundColor = "#BF0000"}
              >
                立即登录
              </Button>
            </Form.Item>
          </Form>

          {/* SSO Login Button - Conditionally Rendered */}
          {import.meta.env.VITE_SSO_ENABLED === 'true' && (
            <>
              <Divider style={{ margin: '20px 0' }}>或</Divider>
              <Form.Item>
                <Button
                  icon={<ApartmentOutlined />} // Example SSO icon
                  onClick={handleSsoLogin}
                  block
                  style={{
                    height: "48px",
                    borderRadius: "6px",
                    fontSize: "16px",
                    fontWeight: "500",
                    // Example: 
                    // backgroundColor: "#f0f2f5", 
                    // borderColor: "#d9d9d9",
                    // color: "#595959"
                  }}
                >
                  通过SSO统一认证登录
                </Button>
              </Form.Item>
            </>
          )}

          <div style={{textAlign: "center", marginTop: "8px", userSelect: "none"}}>
            <Text
              type="secondary"
              style={{
                fontSize: "14px",
                color: "rgba(0, 0, 0, 0.45)"
              }}
            >
              © {new Date().getFullYear()} 北方信托 版权所有
            </Text>
          </div>
        </Card>
      </Content>
    </Layout>
  );
};

export default Login;
