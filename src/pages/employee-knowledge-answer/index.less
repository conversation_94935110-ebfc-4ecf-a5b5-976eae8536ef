// 场景页面 header
.scene-header {
  background-color: #BF0000;
  padding: 0 2rem;
  line-height: 72px;

  // 标题
  .title {
    color: #fff;
    margin: 0;
    font-size: 20px;
    font-weight: bold
  }
}

// 场景页面具体内容
.scene-content {
  padding: 24px;
  flex: 1;

  // 侧边栏
  .sidebar {
    width: 18%;
    height: calc(100vh - 100px);

    // 历史记录列表
    .history-list {
      height: 1000px;
      overflow-y: auto;

      // 历史记录卡片
      .history-item {
        user-select: none;
        background-color: #fff;
        line-height: 28px;
        padding: 0.75rem;
        border-radius: 0.5rem;

        &.active {
          border: 1px solid #BF0000;
        }
      }
    }
  }
}

// 场景页面-用户输入
.scene-employee-input {
  display: flex;
  width: 100%;
  gap: 0.2em;
  align-items: flex-end;
  padding: 16px;
  border-radius: 8px;

  // 文本域样式优化
  .ant-input {
    min-height: 40px;
    line-height: 1.5;
    padding: 8px 12px;

    &:focus {
      border-color: #BF0000;
      box-shadow: 0 0 0 2px rgba(191, 0, 0, 0.1);
    }
  }
}

// 消息列表-用户提问
.query {
  margin: 0;
  font-size: 1rem;
  font-weight: bold;
  color: #000;
}

// 开场白
.markdown-body.center {
  width: 100%;
  padding: 1em;
  background-color: #f5f5f5;
  border-radius: 5px;
}

// 知识库引用标签样式
.knowledge-reference-tag {
  &:hover {
    background-color: #ffe7e7 !important;
    border-color: #ffb3b3 !important;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
  
  &:active {
    transform: translateY(0);
  }
}

// 主内容左对齐样式
.main-content.left {
  margin-left: 18px;
}
