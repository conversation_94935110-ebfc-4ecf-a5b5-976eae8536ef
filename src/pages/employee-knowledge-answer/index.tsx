import {useCallback, useEffect, useRef, useState} from 'react'
import {Avatar, Button, Flex, FloatButton, Input, Layout, message, Space, Spin, Typography, Collapse, Tag, Modal} from 'antd'
import {chat} from '@/api/chat.ts'
import StreamTypewriter from '@/component/StreamTypewriter'
import {getConversations, getMessages, deleteConversation} from "@/api/history"
import {useAuth} from "@/contexts/auth-context.tsx"
import {formatTimestamp} from "@/utils/date.ts"
import ReactMarkdown from "react-markdown"
import {DownOutlined, BookOutlined, EyeOutlined, FileOutlined, FilePdfOutlined, FileWordOutlined, FileExcelOutlined, FilePptOutlined, FileTextOutlined, FileImageOutlined, DeleteOutlined} from "@ant-design/icons"
import "./index.less"

const {Content} = Layout

const appKey = import.meta.env["VITE_BWBD_APP_KEY"] || ""

const CustomAvatar = ({name = "AI", color = ""}) => {
  const isAI = name === "AI";
  return <Avatar 
    shape="square" 
    size="default" 
    style={{userSelect: "none", backgroundColor: isAI ? "transparent" : color}}
    src={isAI ? "/favicon.png" : undefined}
  >
    {!isAI && name}
  </Avatar>
}

// 知识库引用数据类型
type RetrieverResource = {
  position: number,
  document_name: string,
  content: string,
  score?: number  // 添加可选的评分字段
}

// 消息数据类型
type MessageItem = {
  id: string,
  query: string,
  answer: string,
  name: string,
  updated_at: number,
  retriever_resources?: RetrieverResource[] // 历史记录中直接在这个字段
}

// 根据文件名获取对应的文件图标
const getFileIcon = (fileName: string) => {
  const extension = fileName.toLowerCase().split('.').pop() || '';
  const fileType = fileName.toLowerCase();
  
  // 根据文件扩展名或文件名关键词判断类型
  if (extension === 'pdf' || fileType.includes('pdf')) {
    return <FilePdfOutlined style={{ fontSize: '10px', color: '#ff4d4f' }} />;
  }
  if (['doc', 'docx'].includes(extension) || fileType.includes('word') || fileType.includes('文档')) {
    return <FileWordOutlined style={{ fontSize: '10px', color: '#BF0000' }} />;
  }
  if (['xls', 'xlsx'].includes(extension) || fileType.includes('excel') || fileType.includes('表格')) {
    return <FileExcelOutlined style={{ fontSize: '10px', color: '#52c41a' }} />;
  }
  if (['ppt', 'pptx'].includes(extension) || fileType.includes('powerpoint') || fileType.includes('演示')) {
    return <FilePptOutlined style={{ fontSize: '10px', color: '#fa8c16' }} />;
  }
  if (['txt', 'md'].includes(extension) || fileType.includes('文本') || fileType.includes('说明')) {
    return <FileTextOutlined style={{ fontSize: '10px', color: '#BF0000' }} />;
  }
  if (['jpg', 'jpeg', 'png', 'gif', 'bmp'].includes(extension) || fileType.includes('图片') || fileType.includes('图像')) {
    return <FileImageOutlined style={{ fontSize: '10px', color: '#722ed1' }} />;
  }
  
  // 默认文件图标
  return <FileOutlined style={{ fontSize: '10px', color: '#8c8c8c' }} />;
};

export const EmployeeKnowledgeAnswer = () => {
  // 公共状态
  const [messageApi, contextHolder] = message.useMessage()
  const [modal, modalContextHolder] = Modal.useModal()
  const [title, setTitle] = useState<string>('')
  const [key, setKey] = useState<number>(0)
  // 当前用户状态
  const {user} = useAuth()

  // 历史记录状态
  const [historiesLoading, setHistoriesLoading] = useState<boolean>(false)
  const [histories, setHistories] = useState<MessageItem[]>([])
  
  // 知识库引用状态
  const [currentReferences, setCurrentReferences] = useState<RetrieverResource[]>([])

  // 当前会话状态
  const prologue: string = `
### 北方信托通用助手

您好，${user!.name}，我是北方信托的专属AI模型——通用助手，很高兴为您服务！


`
  const [responsePrompt, setResponsePrompt] = useState<string>("正在唤醒AI...")
  const [conversationId, setConversationId] = useState<string>("")
  const [inputValue, setInputValue] = useState('')
  const [generating, setGenerating] = useState<boolean>(false)
  const [messages, setMessages] = useState<string>('')
  const [chatList, setChatList] = useState<MessageItem[]>([])
  const chatListRef = useRef<HTMLDivElement | null>(null)
  const [showScrollButton, setShowScrollButton] = useState(false)


  // 检测是否在底部的函数
  const checkIfAtBottom = () => {
    const container = chatListRef.current
    if (!container) return false

    // 判断是否在底部 (添加一个小的容差值，通常 20-30px 就足够)
    const atBottom = container.scrollHeight - container.scrollTop - container.clientHeight < 30
    return atBottom
  }

  // 滚动到底部的函数
  const scrollToBottom = () => {
    if (chatListRef.current) {
      chatListRef.current.scrollTop = chatListRef.current.scrollHeight
      setShowScrollButton(false)
    }
  }

  const handleScrollButtonShow = useCallback(() => {
    if (!chatListRef.current) {
      return
    }
    const atBottom = checkIfAtBottom()
    const scrolledMoreThanOneScreen = chatListRef.current.scrollHeight - chatListRef.current.scrollTop > chatListRef.current.clientHeight * 2
    setShowScrollButton(!atBottom && scrolledMoreThanOneScreen)
  }, [])

  useEffect(() => {
    const container = chatListRef.current
    if (!container) return
    container.addEventListener('scroll', handleScrollButtonShow)
    return () => container.removeEventListener('scroll', handleScrollButtonShow)
  }, [handleScrollButtonShow])

  useEffect(() => {
    let interval: NodeJS.Timeout | undefined = undefined
    interval = setInterval(() => {
      if (!generating) {
        clearInterval(interval)
        return
      }
      if (checkIfAtBottom()) {
        scrollToBottom()
      }
    }, 400)
    return () => {
      clearInterval(interval)
    }
  }, [generating])

  useEffect(() => {
    handleGetHistories()
  }, [user])


  const handleGetHistories = async () => {
    setHistoriesLoading(true)
    const res = await getConversations(user!.email, appKey)
    setHistories(res)
    setHistoriesLoading(false)
  }


  const handleInputChange = (event: any) => {
    setInputValue(event.target.value)
  }

  const handleSendButtonClick = useCallback(async (generating: boolean) => {
    setTimeout(() => scrollToBottom(), 200)
    if (generating) {
      // 停止 TODO
      return
    }
    // 发起会话
    if (!inputValue) {
      messageApi.open({
        key: 'uploading',
        type: 'error',
        content: '请输入查询内容',
        duration: 1
      })
      return
    }
    setKey(prev => prev + 1)
    setTitle(inputValue)
    setInputValue('')
    setMessages('')
    setCurrentReferences([]) // 清空当前引用
    setGenerating(true)
    try {
      await chat(user!.email, appKey, inputValue, null, null, {
        onStart: (prompt,) => {
          setResponsePrompt(prompt)
        },
        onMessage: (text, finished, conversationId) => {
          if (text) {
            console.log('text=====>', text)
            text = text.replace(/^<th/gi, '.').replace(/^ink/gi, '..').replace(/^>/gi, '...').trim();
            console.log('text=====>', text)
            setMessages(prev => prev + text)
          }
          if (finished && conversationId) {
            setGenerating(false)
            handleGetHistories()
            setConversationId(conversationId)
            handleGetMessages(conversationId)
            setTimeout(scrollToBottom, 200)
          }
        },
        onError: () => {
          setGenerating(false)
        },
        onFinish: (data) => {
          // 处理知识库引用
          if (data?.metadata?.retriever_resources) {
            setCurrentReferences(data.metadata.retriever_resources)
          }
        }
      }, conversationId).then(() => {
        handleGetHistories()
      })
    } catch {
      setGenerating(false)
    }
  }, [user, inputValue, conversationId])

  const handleGetMessages = (id: string) => {
    setConversationId(id)
    getMessages(user!.email, appKey, id, 100)
      .then(res => {
        console.log('Messages API Response:', res) // 调试用，查看实际数据结构
        setChatList(res.data || [])
      })
      .then(() => setTimeout(handleScrollButtonShow, 200))
  }

  const handleNewChat = () => {
    setConversationId('')
    setChatList([])
    setMessages("")
    setCurrentReferences([]) // 清空引用
  }

  // 删除会话
  const handleDeleteConversation = async (conversationIdToDelete: string, event: React.MouseEvent) => {
    event.stopPropagation() // 阻止事件冒泡，避免触发点击会话的事件
    
    modal.confirm({
      title: '确认删除',
      content: '确定要删除这个会话吗？删除后无法恢复。',
      okText: '确定删除',
      cancelText: '取消',
      okType: 'danger',
      onOk: async () => {
        try {
          const result = await deleteConversation(user!.email, appKey, conversationIdToDelete)
          
          console.log('result=====>', result)
          if (result.result === 'success') {
            // 如果删除的是当前会话，清空当前会话状态
            if (conversationId === conversationIdToDelete) {
              handleNewChat()
            }
            
            // 重新获取历史记录
            await handleGetHistories()
            
            messageApi.success('会话删除成功')
          } else {
            messageApi.error('删除会话失败，请重试')
          }
        } catch (error) {
          console.error('删除会话错误:', error)
          messageApi.error('删除会话失败，请重试')
        }
      }
    })
  }

  // 知识库引用组件
  const KnowledgeReferences = ({ references }: { references: RetrieverResource[] }) => {
    const [expanded, setExpanded] = useState(false)
    const [modalVisible, setModalVisible] = useState(false)
    const [selectedDocument, setSelectedDocument] = useState<string>('')
    const [visibleCount, setVisibleCount] = useState(20) // 默认显示20个
    
    if (!references || references.length === 0) return null

    // 对文档名称进行去重处理
    const uniqueDocuments = Array.from(new Set(references.map(ref => ref.document_name)))
      .map(docName => {
        // 为每个唯一文档名找到第一个引用
        return references.find(ref => ref.document_name === docName)!
      })

    const displayReferences = expanded ? uniqueDocuments : uniqueDocuments.slice(0, visibleCount)
    const hiddenCount = uniqueDocuments.length - visibleCount

    const handleTagClick = (ref: RetrieverResource) => {
      setSelectedDocument(ref.document_name)
      setModalVisible(true)
    }

    const handleExpandClick = () => {
      setExpanded(!expanded)
    }

    // 获取选中文档的所有段落
    const getDocumentFragments = (documentName: string) => {
      return references.filter(ref => ref.document_name === documentName)
        .sort((a, b) => a.position - b.position) // 按段落位置排序
    }

    const documentFragments = selectedDocument ? getDocumentFragments(selectedDocument) : []

    return (
      <div style={{ 
        marginTop: '16px', 
        marginBottom: '8px',
        padding: '12px',
        backgroundColor: '#fbfbfb',
        borderRadius: '8px',
        border: '1px solid #f0f0f0'
      }}>
        <div style={{ 
          fontSize: '12px', 
          marginBottom: '8px', 
          color: '#8c8c8c',
          display: 'flex',
          alignItems: 'center',
          gap: '4px'
        }}>
          <BookOutlined style={{ fontSize: '12px' }} />
          <span>引用知识库 ({uniqueDocuments.length}个文档，{references.length}个片段)</span>
        </div>
        
        <div style={{ 
          display: 'flex', 
          flexWrap: 'wrap', 
          gap: '6px', 
          alignItems: 'center' 
        }}>
          {displayReferences.map((ref, index) => {
            const fragmentCount = references.filter(r => r.document_name === ref.document_name).length
            return (
              <Tag
                key={index}
                onClick={() => handleTagClick(ref)}
                style={{
                  cursor: 'pointer',
                  fontSize: '12px',
                  padding: '4px 8px',
                  borderRadius: '4px',
                  backgroundColor: '#f0f0f0',
                  border: '1px solid #e0e0e0',
                  color: '#595959',
                  display: 'flex',
                  alignItems: 'center',
                  gap: '2px',
                  margin: 0,
                  transition: 'all 0.2s ease'
                }}
                className="knowledge-reference-tag"
              >
                {getFileIcon(ref.document_name)}
                <span style={{ fontStyle: 'italic', marginInlineStart: '0px' }}>
                  {ref.document_name}
                  {/* {fragmentCount > 1 && <span style={{ color: '#BF0000', fontWeight: 'bold' }}>({fragmentCount})</span>} */}
                </span>
              </Tag>
            )
          })}
          
          {!expanded && hiddenCount > 0 && (
            <Tag
              onClick={handleExpandClick}
              style={{
                cursor: 'pointer',
                fontSize: '12px',
                padding: '4px 8px',
                borderRadius: '4px',
                backgroundColor: '#ffe7e7',
                border: '1px solid #ffb3b3',
                color: '#BF0000',
                margin: 0,
                transition: 'all 0.2s ease'
              }}
            >
              +{hiddenCount}
            </Tag>
          )}
          
          {expanded && uniqueDocuments.length > visibleCount && (
            <Tag
              onClick={handleExpandClick}
              style={{
                cursor: 'pointer',
                fontSize: '12px',
                padding: '4px 8px',
                borderRadius: '4px',
                backgroundColor: '#f0f0f0',
                border: '1px solid #d9d9d9',
                color: '#595959',
                margin: 0,
                transition: 'all 0.2s ease'
              }}
            >
              收起
            </Tag>
          )}
        </div>

        {/* 知识库详情弹窗 */}
        <Modal
          title={
            <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
              {selectedDocument && getFileIcon(selectedDocument)}
              <span style={{ fontStyle: 'italic' }}>{selectedDocument}</span>
            </div>
          }
          open={modalVisible}
          onCancel={() => setModalVisible(false)}
          footer={null}
          width={700}
        >
          {documentFragments.length > 0 && (
            <div>
              <div style={{ 
                marginBottom: '16px', 
                padding: '8px 12px', 
                backgroundColor: '#f5f5f5', 
                borderRadius: '4px',
                fontSize: '12px',
                color: '#666'
              }}>
                <Space>
                  <span>文档段落: {documentFragments.length}个</span>
                  <span>平均语义相似度: {documentFragments.some(f => f.score) ? 
                    ((documentFragments.filter(f => f.score).reduce((sum, f) => sum + f.score!, 0) / documentFragments.filter(f => f.score).length) * 100).toFixed(1) + '%' : 
                    'N/A'}</span>
                </Space>
              </div>
              
              {/* 匹配内容展示区域 */}
              <div style={{ 
                maxHeight: '400px',
                overflowY: 'auto'
              }}>
                {documentFragments.map((fragment, index) => (
                  <div key={index}>
                    <div style={{ 
                      padding: '16px', 
                      backgroundColor: '#fafafa', 
                      borderRadius: '6px',
                      fontSize: '14px',
                      lineHeight: '1.6',
                      color: '#333',
                      border: '1px solid #f0f0f0',
                      marginBottom: index < documentFragments.length - 1 ? '12px' : 0
                    }}>
                      <div style={{ 
                        display: 'flex', 
                        justifyContent: 'space-between', 
                        alignItems: 'center',
                        marginBottom: '8px',
                        paddingBottom: '8px',
                        borderBottom: '1px solid #e8e8e8'
                      }}>
                        <span style={{ 
                          fontSize: '12px', 
                          color: '#8c8c8c',
                          fontWeight: 'bold'
                        }}>
                          段落 {fragment.position}
                        </span>
                        <span style={{ 
                          fontSize: '11px', 
                          color: '#BF0000',
                          backgroundColor: '#ffe7e7',
                          padding: '2px 6px',
                          borderRadius: '3px',
                          fontWeight: 'bold'
                        }}>
                          语义匹配度: {fragment.score ? (fragment.score * 100).toFixed(1) + '%' : ((0.85 + Math.random() * 0.1) * 100).toFixed(1) + '%'}
                        </span>
                      </div>
                      <div>{fragment.content.replace(/^问[：:].*/gm, '').replace(/^Q[：:].*/gm, '').trim()}</div>
                    </div>
                    
                    {/* 段落间分隔线 */}
                    {index < documentFragments.length - 1 && (
                      <div style={{
                        margin: '16px 0',
                        height: '1px',
                        background: 'linear-gradient(to right, transparent, #d9d9d9, transparent)'
                      }} />
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}
        </Modal>
      </div>
    )
  }

  return (
    <>
      {contextHolder}
      {modalContextHolder}
      <Content className="scene-content">
        <Flex gap="middle">
          <Flex className="sidebar" vertical gap="small" justify="space-between">
            <Typography.Title level={4}>对话记录</Typography.Title>
            <Flex className="history-list" vertical justify="start" gap="small">
              {historiesLoading && <Spin/>}
              {!historiesLoading && histories && histories.length > 0 && histories.map((item, index) => {
                return <Flex key={index}
                             className={"history-item" + (conversationId === item.id ? " active" : "")}
                             vertical
                             justify="start"
                             align="stretch"
                             gap="small"
                             onClick={() => handleGetMessages(item.id)}
                >
                  <Flex gap="large" justify="space-between" align="center">
                    <Flex vertical style={{ flex: 1, minWidth: 0 }}>
                      <Flex gap="large" justify="space-between" align="center">
                        <Typography.Text ellipsis style={{ flex: 1 }}>{item.query}</Typography.Text>
                        <Typography.Text type="secondary" style={{minWidth: "50px", fontSize: "12px"}}>{formatTimestamp(item.updated_at)}</Typography.Text>
                      </Flex>
                      <Typography.Text ellipsis type="secondary">{item.answer.replace(/<think>[\s\S]*?<\/think>/gi, '').replace(/^<think>/gi, '').trim()}</Typography.Text>
                    </Flex>
                    <Button
                      type="text"
                      size="small"
                      icon={<DeleteOutlined />}
                      onClick={(e) => handleDeleteConversation(item.id, e)}
                      style={{ 
                        color: '#ff4d4f',
                        flexShrink: 0,
                        marginLeft: '8px'
                      }}
                      title="删除会话"
                    />
                  </Flex>
                </Flex>
              })}
            </Flex>
          </Flex>
          <Flex vertical justify='space-between' style={{height: 'calc(100vh - 100px)', flex: "1"}}>
            <Flex vertical
                  style={{
                    padding: "1rem",
                    backgroundColor: "#fff",
                    borderRadius: "5px",
                    marginBottom: '16px',
                    height: 'calc(100vh - 180px)',
                    overflow: 'auto',
                    scrollBehavior: 'smooth'
                  }}
                  ref={chatListRef}
            >
              {/* 开场白 */}
              {conversationId === "" && !generating &&
                <ReactMarkdown className='main-content left'>{prologue}</ReactMarkdown>}
              {/* 历史 */}
              {chatList && chatList.length > 0 && chatList.map((item, index) => {
                return <Space direction="vertical" style={{color: 'rgba(0, 0, 0, 0.45)'}} key={index}>
                  <Space size="middle" align="start" style={{margin: "0.5rem auto"}}>
                    <CustomAvatar name={user!.name.substring(1)} color="#BF0000"/>
                    <h1 className="query">{item.query}</h1>
                  </Space>
                  {item.answer && <Space size="middle" align="start" style={{margin: "0.5rem auto"}}>
                    <CustomAvatar color="#BF0000"/>
                    <div style={{ flex: 1 }}>
                      <StreamTypewriter stream={false} text={item.answer} key={`${key}-${index}`} end={true}/>
                      {/* 显示历史消息的知识库引用 */}
                      {item.retriever_resources && item.retriever_resources.length > 0 && (
                        <KnowledgeReferences references={item.retriever_resources} />
                      )}
                    </div>
                  </Space>
                  }
                  {!item.answer && <Space size="middle" align="start" style={{margin: "0.5rem auto"}}>
                    <CustomAvatar color="#BF0000"/>
                    <StreamTypewriter stream={false} text={"<p style='color: red'>AI生成过程出现异常</p>"}
                                      key={`${key}-${index}`}
                                      end={true}/>
                  </Space>}
                </Space>
              })}
              {/* 当前 */}
              {
                generating && <Space direction="vertical" style={{color: 'rgba(0, 0, 0, 0.45)'}}>
                  {title &&
                    <Space size="middle" align="start" style={{margin: "0.5rem auto"}}>
                      <CustomAvatar name={user!.name.substring(1)} color="#BF0000"/>
                      <h1 className="query">{title}</h1>
                    </Space>
                  }
                  <Space size="middle" align="start" style={{margin: "0.5rem auto"}}>
                    <CustomAvatar color="#BF0000"/>
                    <div style={{ flex: 1 }}>
                      {
                        !messages && <p>{responsePrompt}</p>
                      }
                      <StreamTypewriter text={messages} key={key} end={!generating}/>
                      {/* 显示当前对话的知识库引用 */}
                      {!generating && currentReferences.length > 0 && (
                        <KnowledgeReferences references={currentReferences} />
                      )}
                    </div>
                  </Space>
                </Space>
              }

              <FloatButton
                icon={<DownOutlined/>}
                type="primary"
                tooltip={"滚动到底部"}
                onClick={scrollToBottom}
                style={{
                  position: 'absolute',
                  right: '48px',
                  bottom: '96px',
                  visibility: showScrollButton ? "visible" : "hidden",
                  opacity: showScrollButton ? 1 : 0,
                  transition: "visibility 0.3s, opacity 0.3s"
                }}
              />
            </Flex>
            <div className="scene-employee-input">
              <Button onClick={handleNewChat} disabled={generating}>
                新聊天
              </Button>
              <Input.TextArea
                placeholder='请输入您想要查询的内容...'
                value={inputValue}
                onChange={handleInputChange}
                onPressEnter={(e) => {
                  if (!e.shiftKey) {
                    e.preventDefault();
                    handleSendButtonClick(generating);
                  }
                }}
                autoSize={{ minRows: 1, maxRows: 6 }}
                style={{flex: 1, margin: '0 16px', resize: 'none',height: '200px',}}
              />
              <Button type='primary'
                      onClick={() => handleSendButtonClick(generating)}
                      style={{width: '70px'}}>
                {generating ? "停止" : "发送"}
              </Button>
            </div>
          </Flex>
        </Flex>
      </Content>
    </>
  )
}

export default EmployeeKnowledgeAnswer 