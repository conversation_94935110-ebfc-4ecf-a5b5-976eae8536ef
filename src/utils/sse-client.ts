const VITE_AI_API_BASE = import.meta.env['VITE_AI_API_BASE'] || "";


export type SSERequest = {
  type: "chat" | "completion";
  query: string;
  inputs?: Record<string, string>;
  conversationId?: string;
  user: string;
  files?: object[];
  responseMode?: "streaming" | "blocking";
  onStart: (prompt: string, conversation_id: string) => void;
  onMessage: (message: string | null, finished: boolean, conversation_id: string) => void;
  onError: (error: Error) => void;
  onFinish: (data: {
    metadata: {
      retriever_resources: {
        position: number,
        document_name: string,
        content: string
      }[], usage: object
    }, message_id: string
  }) => void;
}

export class SSEClient {
  private readonly token: string;
  private controller: AbortController | null;

  constructor(token: string) {
    this.controller = null;
    this.token = token;
  }

  // 发送聊天消息并处理 SSE 响应
  async sendMessage({
                      type = "chat",
                      query,
                      inputs = {},
                      conversationId = '',
                      user,
                      files = [],
                      responseMode = 'streaming',
                      onStart,
                      onMessage,
                      onError,
                      onFinish
                    }: SSERequest) {
    try {
      // 创建 AbortController 用于取消请求
      this.controller = new AbortController();

      // 准备请求数据
      const requestData = {
        query,
        inputs,
        response_mode: responseMode,
        conversation_id: conversationId,
        user,
        files
      };

      // 发送请求
      const response = await fetch(`${VITE_AI_API_BASE}/${type === "chat" ? "chat-messages" : "completions"}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.token}`
        },
        body: JSON.stringify(requestData),
        signal: this.controller.signal
      });

      if (response === null || response.body === null) throw new Error(`HTTP error!`);
      if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);


      // 获取响应的 ReadableStream
      const reader = response.body.getReader();
      const decoder = new TextDecoder();
      let buffer = '';

      while (true) {
        const {done, value} = await reader.read();

        if (done) {
          break;
        }

        // 解码二进制数据
        buffer += decoder.decode(value, {stream: true});

        // 处理完整的 SSE 消息
        const messages = buffer.split('\n\n');
        buffer = messages.pop() || ''; // 保留最后一个不完整的消息

        for (const message of messages) {
          if (!message.trim() || !message.startsWith('data: ')) continue;

          try {
            // 解析 SSE 消息
            const data = JSON.parse(message.slice(6));

            // 根据不同的事件类型处理消息
            switch (data.event) {
              case 'message':
                onMessage?.(data.answer, false, "");
                break;

              case 'message_end':
                console.log('Message streaming completed', data);
                onMessage?.(null, true, data.conversation_id);
                onFinish?.(data);
                break;

              case 'error':
                onError?.(new Error(data.message));
                break;

              case 'workflow_started':
                onStart("正在唤醒AI...", data.conversation_id);
                break
              case 'node_started':
                if (data.data.node_type === "knowledge-retrieval") {
                  onStart("正在检索知识库...", data.conversation_id);
                }
                break
              case 'node_finished':
                if (data.data.node_type === "knowledge-retrieval") {
                  onStart(`知识库查询完成，共找到${data.data.outputs.result.length}条相关知识段落`, data.conversation_id);
                }
                break
              case 'workflow_finished':
                console.log("完成", data)
                onFinish(data);
                break;
              case 'tts_message':
                // 处理语音合成消息
                if (data.audio) {
                  // 处理 base64 编码的音频数据
                  console.log('Received TTS audio chunk');
                }
                break;
              case 'tts_message_end':
                console.log('TTS streaming completed');
                break;
            }
          } catch (e) {
            console.error('Error parsing SSE message:', e);
          }
        }
      }
    } catch (error) {
      onError?.(error as Error);
    }
  }

  // 取消请求
  cancel() {
    if (this.controller) {
      this.controller.abort();
      this.controller = null;
    }
  }
}

