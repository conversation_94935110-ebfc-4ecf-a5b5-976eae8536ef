/**
 * 将秒级时间戳转换为指定格式的日期时间字符串
 * @param timestamp 秒级时间戳
 * @returns 格式化的日期时间字符串 'yyyy-MM-dd HH:mm:ss'
 */
export function formatTimestamp(timestamp: number): string {
  // 创建日期对象，注意JavaScript使用毫秒时间戳
  const date = new Date(timestamp * 1000);

  // 获取年月日时分秒
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份从0开始
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  // const seconds = String(date.getSeconds()).padStart(2, '0');

  // 返回格式化的字符串
  return `${year}-${month}-${day} ${hours}:${minutes}`;
}
